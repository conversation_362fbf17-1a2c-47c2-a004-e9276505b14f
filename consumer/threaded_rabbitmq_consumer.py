"""
多线程RabbitMQ消费者模块
高性能并发消费市场数据消息
"""

import json
import logging
import time
import uuid
from typing import Dict, Any, List, Tuple
import yaml
import pika
from pika.exceptions import AMQPConnectionError, AMQPChannelError
import threading
from datetime import datetime
from queue import Queue, Empty
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import multiprocessing

from database_factory import get_database_manager


class ThreadedRabbitMQConsumer:
    """多线程RabbitMQ消费者类"""
    
    def __init__(self, config_path: str = "config.yaml", db_config_path: str = "config/database.yaml"):
        """
        初始化多线程RabbitMQ消费者
        
        Args:
            config_path: RabbitMQ配置文件路径
            db_config_path: 数据库配置文件路径
        """
        self.config = self._load_config(config_path)
        self.db_config_path = db_config_path
        self.logger = self._setup_logger()
        
        # RabbitMQ连接（主线程）
        self.connection = None
        self.channel = None
        self.consuming = False
        self._consumer_tag = None
        
        # 多线程配置
        self.thread_pool_size = self.config.get('consumer', {}).get('thread_pool_size', multiprocessing.cpu_count() * 2)
        self.batch_size = self.config.get('consumer', {}).get('batch_size', 100)
        self.batch_timeout = self.config.get('consumer', {}).get('batch_timeout', 1.0)  # 秒
        self.max_queue_size = self.config.get('consumer', {}).get('max_queue_size', 1000)
        
        # 工作队列和线程池
        self.message_queue = Queue(maxsize=self.max_queue_size)
        self.batch_queue = Queue(maxsize=100)
        self.executor = None
        self.batch_processor = None
        
        # 控制标志
        self.shutdown_event = threading.Event()
        self.processing_threads = []
        
        # 统计信息
        self.stats = {
            'messages_received': 0,
            'messages_processed': 0,
            'messages_failed': 0,
            'batches_processed': 0,
            'total_records_processed': 0,
            'start_time': None,
            'thread_pool_size': self.thread_pool_size,
            'batch_size': self.batch_size
        }
        
        # 性能监控
        self.performance_stats = {
            'avg_message_processing_time': 0.0,
            'avg_batch_processing_time': 0.0,
            'queue_depth': 0,
            'active_threads': 0
        }
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            # 确保日志目录存在
            import os
            from pathlib import Path

            log_dir = Path(__file__).parent.parent / 'logs'
            log_dir.mkdir(exist_ok=True)
            log_file = log_dir / 'threaded_consumer.log'

            # 创建格式器
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [Thread-%(thread)d] - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

            # 文件处理器
            try:
                file_handler = logging.FileHandler(
                    log_file,
                    mode='a',
                    encoding='utf-8'
                )
                file_handler.setFormatter(file_formatter)
                logger.addHandler(file_handler)
            except Exception as e:
                print(f"配置消费者日志文件失败: {e}")

            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(file_formatter)
            logger.addHandler(console_handler)

        return logger
    
    def connect(self) -> bool:
        """连接到RabbitMQ服务器"""
        max_retries = 3
        retry_delay = 5

        for attempt in range(max_retries):
            try:
                rabbitmq_config = self.config['rabbitmq']

                # 创建连接参数 - 增加稳定性配置
                credentials = pika.PlainCredentials(
                    rabbitmq_config['username'],
                    rabbitmq_config['password']
                )

                parameters = pika.ConnectionParameters(
                    host=rabbitmq_config['host'],
                    port=rabbitmq_config['port'],
                    virtual_host=rabbitmq_config['virtual_host'],
                    credentials=credentials,
                    heartbeat=300,  # 5分钟心跳，更稳定
                    blocked_connection_timeout=300,
                    socket_timeout=30,
                    connection_attempts=3,
                    retry_delay=2,
                    # 添加TCP keepalive
                    tcp_options={
                        'TCP_KEEPIDLE': 600,
                        'TCP_KEEPINTVL': 30,
                        'TCP_KEEPCNT': 3
                    }
                )

                # 建立连接
                self.connection = pika.BlockingConnection(parameters)
                self.channel = self.connection.channel()

                # 设置QoS - 保守的预取设置
                prefetch_count = min(self.thread_pool_size, 20)  # 更保守的预取
                self.channel.basic_qos(prefetch_count=prefetch_count, global_qos=False)

                # 声明队列确保存在
                queue_name = rabbitmq_config['queue']['name']
                self.channel.queue_declare(
                    queue=queue_name,
                    durable=rabbitmq_config['queue'].get('durable', True),
                    auto_delete=rabbitmq_config['queue'].get('auto_delete', False)
                )

                self.logger.info(f"成功连接到RabbitMQ服务器 (尝试 {attempt + 1}/{max_retries})")
                self.logger.info(f"预取数量: {prefetch_count}, 队列: {queue_name}")
                return True

            except AMQPConnectionError as e:
                self.logger.error(f"连接RabbitMQ失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    self.logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
            except Exception as e:
                self.logger.error(f"连接过程中发生错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2

        self.logger.error("所有连接尝试都失败了")
        return False
    
    def disconnect(self):
        """断开连接"""
        try:
            # 设置关闭标志
            self.shutdown_event.set()
            
            # 停止消费
            if self.consuming:
                self.stop_consuming()
            
            # 关闭线程池
            if self.executor:
                self.logger.info("关闭线程池...")
                self.executor.shutdown(wait=True, timeout=30)
            
            # 等待批处理线程结束
            if self.batch_processor and self.batch_processor.is_alive():
                self.logger.info("等待批处理线程结束...")
                self.batch_processor.join(timeout=10)

            # 等待消息确认处理线程结束
            if hasattr(self, 'ack_processor') and self.ack_processor and self.ack_processor.is_alive():
                self.logger.info("等待消息确认处理线程结束...")
                self.ack_processor.join(timeout=5)
            
            # 关闭RabbitMQ连接
            if self.connection and not self.connection.is_closed:
                self.connection.close()
                self.logger.info("已断开RabbitMQ连接")
                
        except Exception as e:
            self.logger.error(f"断开连接时发生错误: {e}")
    
    def _on_message_received(self, channel, method, properties, body):
        """
        消息接收回调（在主线程中执行）
        快速将消息放入队列，避免阻塞RabbitMQ
        """
        try:
            # 快速统计
            self.stats['messages_received'] += 1

            # 将消息放入处理队列，包含确认信息
            message_item = {
                'delivery_tag': method.delivery_tag,
                'body': body,
                'properties': properties,
                'received_time': time.time(),
                'message_id': str(uuid.uuid4()),
                'routing_key': method.routing_key,
                'exchange': method.exchange
            }

            # 非阻塞放入队列
            try:
                self.message_queue.put_nowait(message_item)
                self.logger.debug(f"消息已放入队列: {message_item['message_id']}")
                # 不在这里确认消息，等待批处理完成后统一确认
            except:
                # 队列满了，拒绝消息并重新入队
                self.logger.warning("消息队列已满，拒绝消息")
                channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

        except Exception as e:
            self.logger.error(f"接收消息时发生错误: {e}")
            # 拒绝消息
            try:
                channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
            except:
                pass
    
    def _process_message_batch(self, messages: List[Dict]) -> Tuple[int, int, List[int]]:
        """
        批量处理消息

        Args:
            messages: 消息列表

        Returns:
            Tuple[int, int, List[int]]: (成功数量, 总数量, 成功的delivery_tags)
        """
        if not messages:
            return 0, 0, []

        start_time = time.time()
        success_count = 0
        total_count = len(messages)
        successful_delivery_tags = []

        # 获取数据库管理器（每个线程一个）
        db_manager = get_database_manager(self.db_config_path)

        try:
            # 准备批量数据
            batch_quotes = []

            for message_item in messages:
                try:
                    # 解析消息
                    message_data = json.loads(message_item['body'].decode('utf-8'))

                    # 提取数据
                    if 'data' in message_data:
                        market_data = message_data['data']
                    else:
                        market_data = message_data

                    # 转换为批量格式
                    for symbol, quote_data_list in market_data.items():
                        if isinstance(quote_data_list, list):
                            for quote_data in quote_data_list:
                                batch_quotes.append({
                                    'symbol': symbol,
                                    'quote_data': quote_data,
                                    'message_id': message_item['message_id']
                                })

                except Exception as e:
                    self.logger.error(f"解析消息失败: {e}")
                    continue

            # 批量插入数据库
            if batch_quotes:
                if db_manager.batch_insert_market_quotes(batch_quotes):
                    success_count = total_count
                    self.stats['total_records_processed'] += len(batch_quotes)
                    # 收集成功处理的delivery tags
                    successful_delivery_tags = [msg['delivery_tag'] for msg in messages]
                    self.logger.debug(f"批量处理成功: {len(batch_quotes)} 条记录")
                else:
                    self.logger.warning(f"批量插入失败: {len(batch_quotes)} 条记录")
                    # 如果数据库插入失败，不收集delivery tags，这样消息会被重新处理

            # 更新统计
            processing_time = time.time() - start_time
            self.performance_stats['avg_batch_processing_time'] = (
                self.performance_stats['avg_batch_processing_time'] * 0.9 + processing_time * 0.1
            )

            return success_count, total_count, successful_delivery_tags

        except Exception as e:
            self.logger.error(f"批量处理消息失败: {e}")
            return 0, total_count, []
        finally:
            # 关闭数据库连接
            try:
                db_manager.close()
            except:
                pass
    
    def _batch_processor_worker(self):
        """批处理工作线程"""
        self.logger.info(f"批处理线程启动，批大小: {self.batch_size}, 超时: {self.batch_timeout}s")

        while not self.shutdown_event.is_set():
            try:
                batch_messages = []
                batch_start_time = time.time()

                # 收集批量消息
                while (len(batch_messages) < self.batch_size and
                       time.time() - batch_start_time < self.batch_timeout and
                       not self.shutdown_event.is_set()):

                    try:
                        # 从队列获取消息
                        timeout = max(0.1, self.batch_timeout - (time.time() - batch_start_time))
                        message_item = self.message_queue.get(timeout=timeout)
                        batch_messages.append(message_item)

                        # 更新队列深度统计
                        self.performance_stats['queue_depth'] = self.message_queue.qsize()

                    except Empty:
                        # 超时，处理当前批次
                        break

                # 处理批次
                if batch_messages:
                    success_count, total_count, successful_delivery_tags = self._process_message_batch(batch_messages)

                    # 将确认信息放入确认队列，由主线程处理
                    if successful_delivery_tags:
                        try:
                            self.batch_queue.put_nowait({
                                'type': 'ack',
                                'delivery_tags': successful_delivery_tags,
                                'timestamp': time.time()
                            })
                        except:
                            self.logger.warning("确认队列已满，可能导致消息重复处理")

                    # 处理失败的消息
                    failed_delivery_tags = []
                    for i, message_item in enumerate(batch_messages):
                        if message_item['delivery_tag'] not in successful_delivery_tags:
                            failed_delivery_tags.append(message_item['delivery_tag'])

                    if failed_delivery_tags:
                        try:
                            self.batch_queue.put_nowait({
                                'type': 'nack',
                                'delivery_tags': failed_delivery_tags,
                                'timestamp': time.time()
                            })
                        except:
                            self.logger.warning("确认队列已满，失败消息可能无法正确处理")

                    # 更新统计
                    self.stats['batches_processed'] += 1
                    self.stats['messages_processed'] += success_count
                    self.stats['messages_failed'] += (total_count - success_count)

                    self.logger.info(f"批处理完成: {success_count}/{total_count} 成功, "
                                   f"批次大小: {len(batch_messages)}, "
                                   f"队列深度: {self.message_queue.qsize()}")

            except Exception as e:
                self.logger.error(f"批处理工作线程错误: {e}")
                time.sleep(1)  # 避免错误循环

        self.logger.info("批处理线程结束")

    def _ack_processor_worker(self):
        """消息确认处理线程（在主线程中运行）"""
        self.logger.info("消息确认处理线程启动")

        while not self.shutdown_event.is_set():
            try:
                # 处理确认队列中的消息
                try:
                    ack_item = self.batch_queue.get(timeout=0.1)

                    # 检查连接状态
                    if not self.connection or self.connection.is_closed:
                        self.logger.warning("RabbitMQ连接已关闭，跳过消息确认")
                        continue

                    if not self.channel or self.channel.is_closed:
                        self.logger.warning("RabbitMQ通道已关闭，跳过消息确认")
                        continue

                    if ack_item['type'] == 'ack':
                        # 批量确认成功的消息
                        delivery_tags = ack_item['delivery_tags']
                        if delivery_tags:
                            try:
                                # 使用最大的delivery tag进行批量确认
                                max_tag = max(delivery_tags)
                                self.channel.basic_ack(delivery_tag=max_tag, multiple=True)
                                self.logger.debug(f"批量确认消息: tags <= {max_tag}")
                            except Exception as e:
                                self.logger.error(f"批量确认消息失败: {e}")
                                # 如果批量确认失败，尝试逐个确认
                                for delivery_tag in delivery_tags:
                                    try:
                                        self.channel.basic_ack(delivery_tag=delivery_tag)
                                    except Exception as e2:
                                        self.logger.error(f"确认消息失败 (tag: {delivery_tag}): {e2}")

                    elif ack_item['type'] == 'nack':
                        # 拒绝失败的消息
                        for delivery_tag in ack_item['delivery_tags']:
                            try:
                                self.channel.basic_nack(delivery_tag=delivery_tag, requeue=True)
                            except Exception as e:
                                self.logger.error(f"拒绝消息失败 (tag: {delivery_tag}): {e}")

                except Empty:
                    # 没有待处理的确认消息，继续循环
                    continue

            except Exception as e:
                self.logger.error(f"消息确认处理线程错误: {e}")
                # 如果是连接错误，尝试重连
                if "connection" in str(e).lower() or "channel" in str(e).lower():
                    self.logger.warning("检测到连接问题，将触发重连")
                    self.shutdown_event.set()  # 触发重连
                    break
                time.sleep(0.1)

        self.logger.info("消息确认处理线程结束")

    def _reconnect(self) -> bool:
        """重新连接到RabbitMQ"""
        self.logger.info("尝试重新连接到RabbitMQ...")

        # 关闭现有连接
        try:
            if self.channel and not self.channel.is_closed:
                self.channel.close()
        except:
            pass

        try:
            if self.connection and not self.connection.is_closed:
                self.connection.close()
        except:
            pass

        # 重置连接对象
        self.connection = None
        self.channel = None
        self.consuming = False

        # 等待一段时间再重连
        time.sleep(5)

        # 尝试重新连接
        if self.connect():
            self.logger.info("重新连接成功")
            return True
        else:
            self.logger.error("重新连接失败")
            return False

    def start_consuming(self):
        """开始消费消息"""
        max_reconnect_attempts = 5
        reconnect_count = 0

        while not self.shutdown_event.is_set() and reconnect_count < max_reconnect_attempts:
            try:
                # 确保连接正常
                if not self.connection or self.connection.is_closed:
                    if not self.connect():
                        raise Exception("无法连接到RabbitMQ服务器")

                # 重置关闭事件
                self.shutdown_event.clear()

                # 启动批处理线程
                if not (self.batch_processor and self.batch_processor.is_alive()):
                    self.batch_processor = threading.Thread(
                        target=self._batch_processor_worker,
                        name="BatchProcessor",
                        daemon=True
                    )
                    self.batch_processor.start()

                # 启动消息确认处理线程
                if not (hasattr(self, 'ack_processor') and self.ack_processor and self.ack_processor.is_alive()):
                    self.ack_processor = threading.Thread(
                        target=self._ack_processor_worker,
                        name="AckProcessor",
                        daemon=True
                    )
                    self.ack_processor.start()

                # 设置消费者
                queue_name = self.config['rabbitmq']['queue']['name']
                self._consumer_tag = self.channel.basic_consume(
                    queue=queue_name,
                    on_message_callback=self._on_message_received
                )

                self.consuming = True
                if not self.stats['start_time']:
                    self.stats['start_time'] = datetime.now()

                self.logger.info(f"多线程消费者启动 (重连次数: {reconnect_count}):")
                self.logger.info(f"  队列: {queue_name}")
                self.logger.info(f"  线程池大小: {self.thread_pool_size}")
                self.logger.info(f"  批处理大小: {self.batch_size}")
                self.logger.info(f"  批处理超时: {self.batch_timeout}s")
                self.logger.info(f"  最大队列大小: {self.max_queue_size}")
                self.logger.info("等待消息... 按 CTRL+C 停止")

                # 重置重连计数
                reconnect_count = 0

                # 开始消费
                self.channel.start_consuming()

            except KeyboardInterrupt:
                self.logger.info("接收到中断信号，正在停止消费...")
                self.stop_consuming()
                break
            except Exception as e:
                self.logger.error(f"消费过程中发生错误: {e}")

                # 检查是否是连接相关错误
                error_str = str(e).lower()
                if any(keyword in error_str for keyword in ['connection', 'stream', 'socket', 'transport', 'channel']):
                    reconnect_count += 1
                    self.logger.warning(f"检测到连接错误，尝试重连 ({reconnect_count}/{max_reconnect_attempts})")

                    if reconnect_count < max_reconnect_attempts:
                        if self._reconnect():
                            continue  # 重连成功，继续消费
                        else:
                            self.logger.error("重连失败，等待后重试")
                            time.sleep(10)
                    else:
                        self.logger.error("达到最大重连次数，停止消费")
                        break
                else:
                    # 非连接错误，直接抛出
                    raise
    
    def stop_consuming(self):
        """停止消费消息"""
        if self.consuming and self.channel:
            try:
                self.channel.stop_consuming()
                self.consuming = False
                self.logger.info("已停止消费消息")
            except Exception as e:
                self.logger.error(f"停止消费时发生错误: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats.update(self.performance_stats.copy())
        
        if stats['start_time']:
            runtime = datetime.now() - stats['start_time']
            stats['runtime_seconds'] = runtime.total_seconds()
            
            if stats['runtime_seconds'] > 0:
                stats['messages_per_second'] = stats['messages_received'] / stats['runtime_seconds']
                stats['records_per_second'] = stats['total_records_processed'] / stats['runtime_seconds']
            else:
                stats['messages_per_second'] = 0
                stats['records_per_second'] = 0
        
        # 添加实时统计
        stats['queue_depth'] = self.message_queue.qsize()
        stats['batch_queue_depth'] = self.batch_queue.qsize()
        
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查RabbitMQ连接
            rabbitmq_status = 'healthy' if (self.connection and not self.connection.is_closed) else 'unhealthy'
            
            # 检查数据库连接
            db_manager = get_database_manager(self.db_config_path)
            db_health = db_manager.health_check()
            
            # 检查消费状态
            consumer_status = 'consuming' if self.consuming else 'stopped'
            
            # 检查线程状态
            batch_processor_status = 'running' if (self.batch_processor and self.batch_processor.is_alive()) else 'stopped'
            ack_processor_status = 'running' if (hasattr(self, 'ack_processor') and self.ack_processor and self.ack_processor.is_alive()) else 'stopped'

            return {
                'status': 'healthy' if rabbitmq_status == 'healthy' and db_health['status'] == 'healthy' else 'unhealthy',
                'rabbitmq': rabbitmq_status,
                'database': db_health,
                'consumer': consumer_status,
                'batch_processor': batch_processor_status,
                'ack_processor': ack_processor_status,
                'stats': self.get_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }


# 全局消费者实例
_threaded_consumer = None


def get_threaded_consumer(config_path: str = "config.yaml", db_config_path: str = "config/database.yaml") -> ThreadedRabbitMQConsumer:
    """获取多线程消费者单例"""
    global _threaded_consumer
    if _threaded_consumer is None:
        _threaded_consumer = ThreadedRabbitMQConsumer(config_path, db_config_path)
    return _threaded_consumer


def close_threaded_consumer():
    """关闭多线程消费者"""
    global _threaded_consumer
    if _threaded_consumer:
        _threaded_consumer.disconnect()
        _threaded_consumer = None
