"""
数据库接口抽象类
定义统一的数据库操作接口，支持MySQL和ClickHouse
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
import logging


class DatabaseInterface(ABC):
    """数据库接口抽象类"""
    
    def __init__(self, config_path: str):
        """
        初始化数据库接口
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(self.__class__.__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    @abstractmethod
    def insert_market_quote(self, symbol: str, quote_data: Dict[str, Any]) -> bool:
        """
        插入市场行情数据
        
        Args:
            symbol: 股票代码
            quote_data: 行情数据
            
        Returns:
            bool: 插入是否成功
        """
        pass
    
    @abstractmethod
    def log_processing_result(self, message_id: str, symbol: str, status: str,
                            error_message: str = None, raw_data: Dict = None) -> bool:
        """
        记录处理结果
        
        Args:
            message_id: 消息ID
            symbol: 股票代码
            status: 处理状态
            error_message: 错误信息
            raw_data: 原始数据
            
        Returns:
            bool: 记录是否成功
        """
        pass
    
    @abstractmethod
    def get_latest_quotes(self, symbols: List[str] = None) -> List[Dict[str, Any]]:
        """
        获取最新行情数据
        
        Args:
            symbols: 股票代码列表，为None时获取所有
            
        Returns:
            List[Dict]: 行情数据列表
        """
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """
        数据库健康检查
        
        Returns:
            Dict: 健康状态信息
        """
        pass
    
    @abstractmethod
    def close(self):
        """关闭数据库连接"""
        pass
    
    @abstractmethod
    def get_database_type(self) -> str:
        """
        获取数据库类型
        
        Returns:
            str: 数据库类型 ('mysql' 或 'clickhouse')
        """
        pass
    
    def batch_insert_market_quotes(self, quotes: List[Dict[str, Any]]) -> bool:
        """
        批量插入行情数据（默认实现，子类可重写以优化性能）
        
        Args:
            quotes: 行情数据列表，每个元素包含symbol和quote_data
            
        Returns:
            bool: 批量插入是否成功
        """
        success_count = 0
        for quote in quotes:
            symbol = quote.get('symbol')
            quote_data = quote.get('quote_data')
            if symbol and quote_data:
                if self.insert_market_quote(symbol, quote_data):
                    success_count += 1
        
        return success_count == len(quotes)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息（默认实现）

        Returns:
            Dict: 统计信息
        """
        return {
            'database_type': self.get_database_type(),
            'health': self.health_check(),
            'pool_size': self.get_pool_size()
        }

    def get_pool_size(self) -> int:
        """
        获取连接池大小（默认实现）

        Returns:
            int: 连接池大小
        """
        return 0
