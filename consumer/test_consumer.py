"""
消费者服务单元测试
"""

import pytest
import json
import tempfile
import os
import time
import threading
from unittest.mock import Mock, patch, MagicMock, call
import yaml
from datetime import datetime

# 导入被测试的模块
from database_manager import DatabaseManager, get_database_manager, close_database_manager
from rabbitmq_consumer import RabbitMQConsumer, get_consumer, close_consumer
from metrics import MetricsCollector, get_metrics_collector
from health_check import HealthCheckServer, HealthCheckHandler


class TestDatabaseManager:
    """数据库管理器测试类"""
    
    @pytest.fixture
    def db_config_data(self):
        """测试数据库配置"""
        return {
            'database': {
                'primary': {
                    'host': 'localhost',
                    'port': 3306,
                    'username': 'test_user',
                    'password': 'test_pass',
                    'database': 'test_db',
                    'charset': 'utf8mb4'
                },
                'pool': {
                    'min_connections': 2,
                    'max_connections': 10,
                    'max_idle_time': 300,
                    'max_lifetime': 3600
                },
                'connection': {
                    'connect_timeout': 10,
                    'read_timeout': 30,
                    'write_timeout': 30,
                    'autocommit': True
                }
            }
        }
    
    @pytest.fixture
    def db_config_file(self, db_config_data):
        """创建临时数据库配置文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(db_config_data, f)
            config_path = f.name
        
        yield config_path
        
        if os.path.exists(config_path):
            os.unlink(config_path)
    
    @patch('consumer.database_manager.pymysql.connect')
    def test_create_connection(self, mock_connect, db_config_file):
        """测试创建数据库连接"""
        mock_conn = Mock()
        mock_connect.return_value = mock_conn
        
        db_manager = DatabaseManager(db_config_file)
        conn = db_manager._create_connection()
        
        assert conn == mock_conn
        mock_connect.assert_called_once()
    
    @patch('consumer.database_manager.pymysql.connect')
    def test_get_connection_context_manager(self, mock_connect, db_config_file):
        """测试连接上下文管理器"""
        mock_conn = Mock()
        mock_connect.return_value = mock_conn
        mock_conn.ping.return_value = None
        
        db_manager = DatabaseManager(db_config_file)
        
        with db_manager.get_connection() as conn:
            assert conn == mock_conn
        
        # 验证连接被放回连接池
        assert db_manager._connection_pool.qsize() >= 0
    
    @patch('consumer.database_manager.pymysql.connect')
    def test_insert_market_quote_success(self, mock_connect, db_config_file):
        """测试成功插入市场行情数据"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect.return_value = mock_conn
        mock_conn.cursor.return_value = mock_cursor
        mock_conn.ping.return_value = None
        
        db_manager = DatabaseManager(db_config_file)
        
        # 测试数据
        symbol = "600000.SH"
        quote_data = {
            'time': 1750316403000,
            'timetag': '20250619 15:00:03',
            'lastPrice': 12.74,
            'open': 12.78,
            'high': 12.84,
            'low': 12.64,
            'lastClose': 12.78,
            'amount': 557599600,
            'volume': 437681,
            'pvolume': 43768050,
            'stockStatus': 5,
            'openInt': 15,
            'settlementPrice': 0,
            'lastSettlementPrice': 12.78,
            'askPrice': [0, 0, 0, 0, 0],
            'bidPrice': [0, 0, 0, 0, 0],
            'askVol': [0, 0, 0, 0, 0],
            'bidVol': [0, 0, 0, 0, 0]
        }
        
        result = db_manager.insert_market_quote(symbol, quote_data)
        
        assert result is True
        assert mock_cursor.execute.call_count == 2  # 两次SQL执行（主表和盘口表）
        mock_conn.commit.assert_called_once()
    
    @patch('consumer.database_manager.pymysql.connect')
    def test_insert_market_quote_failure(self, mock_connect, db_config_file):
        """测试插入市场行情数据失败"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect.return_value = mock_conn
        mock_conn.cursor.return_value = mock_cursor
        mock_conn.ping.return_value = None
        mock_cursor.execute.side_effect = Exception("Database error")
        
        db_manager = DatabaseManager(db_config_file)
        
        symbol = "600000.SH"
        quote_data = {'time': 1750316403000}
        
        result = db_manager.insert_market_quote(symbol, quote_data)
        
        assert result is False
    
    @patch('consumer.database_manager.pymysql.connect')
    def test_health_check_healthy(self, mock_connect, db_config_file):
        """测试健康检查 - 健康状态"""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_connect.return_value = mock_conn
        mock_conn.cursor.return_value = mock_cursor
        mock_conn.ping.return_value = None
        
        # 模拟查询结果
        mock_cursor.fetchone.side_effect = [
            {'Rows': 1000},  # 表状态
            {'latest_time': datetime.now()}  # 最新数据时间
        ]
        
        db_manager = DatabaseManager(db_config_file)
        health = db_manager.health_check()
        
        assert health['status'] == 'healthy'
        assert health['connection'] == 'ok'
        assert 'table_rows' in health
        assert 'latest_data_time' in health
    
    @patch('consumer.database_manager.pymysql.connect')
    def test_health_check_unhealthy(self, mock_connect, db_config_file):
        """测试健康检查 - 不健康状态"""
        mock_connect.side_effect = Exception("Connection failed")
        
        db_manager = DatabaseManager(db_config_file)
        health = db_manager.health_check()
        
        assert health['status'] == 'unhealthy'
        assert 'error' in health


class TestRabbitMQConsumer:
    """RabbitMQ消费者测试类"""
    
    @pytest.fixture
    def rabbitmq_config_data(self):
        """测试RabbitMQ配置"""
        return {
            'rabbitmq': {
                'host': 'localhost',
                'port': 5672,
                'username': 'test_user',
                'password': 'test_pass',
                'virtual_host': '/',
                'queue': {
                    'name': 'test_queue',
                    'durable': True,
                    'auto_delete': False,
                    'exclusive': False
                },
                'exchange': {
                    'name': 'test_exchange',
                    'type': 'direct',
                    'durable': True,
                    'auto_delete': False
                },
                'routing_key': 'test.key',
                'connection': {
                    'heartbeat': 600,
                    'blocked_connection_timeout': 300,
                    'socket_timeout': 10
                }
            }
        }
    
    @pytest.fixture
    def rabbitmq_config_file(self, rabbitmq_config_data):
        """创建临时RabbitMQ配置文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(rabbitmq_config_data, f)
            config_path = f.name
        
        yield config_path
        
        if os.path.exists(config_path):
            os.unlink(config_path)
    
    @pytest.fixture
    def mock_db_manager(self):
        """模拟数据库管理器"""
        with patch('consumer.rabbitmq_consumer.get_database_manager') as mock_get_db:
            mock_db = Mock()
            mock_db.insert_market_quote.return_value = True
            mock_db.log_processing_result.return_value = True
            mock_get_db.return_value = mock_db
            yield mock_db
    
    @patch('consumer.rabbitmq_consumer.pika.BlockingConnection')
    def test_connect_success(self, mock_connection, rabbitmq_config_file, mock_db_manager):
        """测试成功连接RabbitMQ"""
        mock_conn = Mock()
        mock_channel = Mock()
        mock_connection.return_value = mock_conn
        mock_conn.channel.return_value = mock_channel
        
        consumer = RabbitMQConsumer(rabbitmq_config_file)
        result = consumer.connect()
        
        assert result is True
        assert consumer.connection == mock_conn
        assert consumer.channel == mock_channel
        mock_channel.basic_qos.assert_called_once_with(prefetch_count=10)
    
    @patch('consumer.rabbitmq_consumer.pika.BlockingConnection')
    def test_connect_failure(self, mock_connection, rabbitmq_config_file, mock_db_manager):
        """测试连接RabbitMQ失败"""
        from pika.exceptions import AMQPConnectionError
        mock_connection.side_effect = AMQPConnectionError("Connection failed")
        
        consumer = RabbitMQConsumer(rabbitmq_config_file)
        result = consumer.connect()
        
        assert result is False
        assert consumer.connection is None
        assert consumer.channel is None
    
    def test_process_message_success(self, rabbitmq_config_file, mock_db_manager):
        """测试成功处理消息"""
        consumer = RabbitMQConsumer(rabbitmq_config_file)
        
        # 模拟消息数据
        message_data = {
            'timestamp': '2025-06-19T15:00:03',
            'data': {
                '600000.SH': {
                    'time': 1750316403000,
                    'timetag': '20250619 15:00:03',
                    'lastPrice': 12.74,
                    'open': 12.78,
                    'high': 12.84,
                    'low': 12.64
                }
            }
        }
        
        # 模拟通道和方法
        mock_channel = Mock()
        mock_method = Mock()
        mock_method.delivery_tag = 'test_tag'
        mock_properties = Mock()
        
        message_body = json.dumps(message_data).encode('utf-8')
        
        result = consumer._process_message(mock_channel, mock_method, mock_properties, message_body)
        
        assert result is True
        mock_db_manager.insert_market_quote.assert_called_once()
        mock_channel.basic_ack.assert_called_once_with(delivery_tag='test_tag')
    
    def test_process_message_invalid_json(self, rabbitmq_config_file, mock_db_manager):
        """测试处理无效JSON消息"""
        consumer = RabbitMQConsumer(rabbitmq_config_file)
        
        mock_channel = Mock()
        mock_method = Mock()
        mock_method.delivery_tag = 'test_tag'
        mock_properties = Mock()
        
        # 无效JSON
        message_body = b"invalid json"
        
        result = consumer._process_message(mock_channel, mock_method, mock_properties, message_body)
        
        assert result is False
        mock_channel.basic_nack.assert_called_once_with(delivery_tag='test_tag', requeue=False)
    
    def test_get_stats(self, rabbitmq_config_file, mock_db_manager):
        """测试获取统计信息"""
        consumer = RabbitMQConsumer(rabbitmq_config_file)
        consumer.stats['start_time'] = datetime.now()
        consumer.stats['messages_received'] = 100
        consumer.stats['messages_processed'] = 95
        consumer.stats['messages_failed'] = 5
        
        stats = consumer.get_stats()
        
        assert stats['messages_received'] == 100
        assert stats['messages_processed'] == 95
        assert stats['messages_failed'] == 5
        assert 'runtime_seconds' in stats
        assert 'messages_per_second' in stats


class TestMetricsCollector:
    """指标收集器测试类"""
    
    @patch('consumer.metrics.start_http_server')
    def test_init_metrics_collector(self, mock_start_server):
        """测试初始化指标收集器"""
        collector = MetricsCollector(port=9000)
        
        assert collector.port == 9000
        mock_start_server.assert_called_once_with(9000)
        
        # 验证指标已初始化
        assert hasattr(collector, 'messages_received_total')
        assert hasattr(collector, 'messages_processed_total')
        assert hasattr(collector, 'database_operations_total')
    
    @patch('consumer.metrics.start_http_server')
    def test_record_message_metrics(self, mock_start_server):
        """测试记录消息指标"""
        collector = MetricsCollector(port=9000)
        
        # 记录接收消息
        collector.record_message_received('test_queue', 'success')
        
        # 记录处理消息
        collector.record_message_processed('600000.SH', 0.5)
        
        # 记录失败消息
        collector.record_message_failed('json_error', '600000.SH')
        
        # 验证指标被调用（这里只能验证方法被调用，实际值需要集成测试）
        assert collector.messages_received_total is not None
        assert collector.messages_processed_total is not None
        assert collector.messages_failed_total is not None
    
    @patch('consumer.metrics.start_http_server')
    def test_update_system_metrics(self, mock_start_server):
        """测试更新系统指标"""
        collector = MetricsCollector(port=9000)
        
        collector.update_system_metrics()
        
        # 验证系统指标被更新
        assert collector.system_uptime_seconds is not None
        assert collector.active_threads is not None


class TestHealthCheckHandler:
    """健康检查处理器测试类"""
    
    @pytest.fixture
    def mock_consumer(self):
        """模拟消费者"""
        with patch('consumer.health_check.get_consumer') as mock_get_consumer:
            mock_consumer = Mock()
            mock_consumer.health_check.return_value = {
                'status': 'healthy',
                'rabbitmq': 'healthy',
                'database': {'status': 'healthy'},
                'consumer': 'consuming'
            }
            mock_consumer.get_stats.return_value = {
                'messages_received': 100,
                'messages_processed': 95
            }
            mock_get_consumer.return_value = mock_consumer
            yield mock_consumer
    
    @pytest.fixture
    def mock_metrics(self):
        """模拟指标收集器"""
        with patch('consumer.health_check.get_metrics_collector') as mock_get_metrics:
            mock_metrics = Mock()
            mock_metrics.port = 8000
            mock_metrics.start_time = time.time() - 3600  # 1小时前启动
            mock_metrics.get_metrics_summary.return_value = {
                'uptime_seconds': 3600,
                'active_threads': 5
            }
            mock_get_metrics.return_value = mock_metrics
            yield mock_metrics
    
    def test_health_check_basic(self, mock_consumer, mock_metrics):
        """测试基本健康检查"""
        # 这里需要更复杂的测试设置来模拟HTTP请求
        # 由于HealthCheckHandler继承自BaseHTTPRequestHandler，需要特殊的测试方法
        pass  # 实际项目中可以使用测试客户端或集成测试
    
    def test_health_server_start_stop(self):
        """测试健康检查服务器启动和停止"""
        server = HealthCheckServer(host='127.0.0.1', port=0)  # 使用端口0让系统分配
        
        # 启动服务器
        server.start()
        assert server.is_running()
        
        # 停止服务器
        server.stop()
        time.sleep(0.1)  # 等待服务器完全停止
        assert not server.is_running()


class TestIntegration:
    """集成测试"""
    
    @pytest.fixture
    def sample_market_data(self):
        """示例市场数据"""
        return {
            '600000.SH': {
                'time': 1750316403000,
                'timetag': '20250619 15:00:03',
                'lastPrice': 12.74,
                'open': 12.78,
                'high': 12.84,
                'low': 12.64,
                'lastClose': 12.78,
                'amount': 557599600,
                'volume': 437681,
                'pvolume': 43768050,
                'stockStatus': 5,
                'openInt': 15,
                'settlementPrice': 0,
                'lastSettlementPrice': 12.78,
                'askPrice': [0, 0, 0, 0, 0],
                'bidPrice': [0, 0, 0, 0, 0],
                'askVol': [0, 0, 0, 0, 0],
                'bidVol': [0, 0, 0, 0, 0]
            }
        }
    
    @patch('consumer.database_manager.pymysql.connect')
    @patch('consumer.rabbitmq_consumer.pika.BlockingConnection')
    def test_end_to_end_message_processing(self, mock_rabbitmq_conn, mock_db_conn, 
                                         sample_market_data, rabbitmq_config_file, db_config_file):
        """测试端到端消息处理"""
        # 设置数据库模拟
        mock_db_connection = Mock()
        mock_db_cursor = Mock()
        mock_db_conn.return_value = mock_db_connection
        mock_db_connection.cursor.return_value = mock_db_cursor
        mock_db_connection.ping.return_value = None
        
        # 设置RabbitMQ模拟
        mock_rabbitmq_connection = Mock()
        mock_channel = Mock()
        mock_rabbitmq_conn.return_value = mock_rabbitmq_connection
        mock_rabbitmq_connection.channel.return_value = mock_channel
        mock_rabbitmq_connection.is_closed = False
        
        # 创建消费者
        consumer = RabbitMQConsumer(rabbitmq_config_file, db_config_file)
        consumer.connect()
        
        # 模拟消息处理
        message_data = {
            'timestamp': '2025-06-19T15:00:03',
            'data': sample_market_data
        }
        
        mock_method = Mock()
        mock_method.delivery_tag = 'test_tag'
        mock_properties = Mock()
        message_body = json.dumps(message_data).encode('utf-8')
        
        # 处理消息
        result = consumer._process_message(mock_channel, mock_method, mock_properties, message_body)
        
        # 验证结果
        assert result is True
        assert mock_db_cursor.execute.call_count >= 2  # 至少执行了主表和盘口表插入
        mock_db_connection.commit.assert_called()
        mock_channel.basic_ack.assert_called_once_with(delivery_tag='test_tag')


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
