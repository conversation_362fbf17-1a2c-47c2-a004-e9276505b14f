"""
MySQL数据库管理器
用于处理市场数据的存储和查询
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import yaml
import pymysql
from pymysql.cursors import DictCursor
from contextlib import contextmanager
import threading
from queue import Queue, Empty
import json

from database_interface import DatabaseInterface


class DatabaseManager(DatabaseInterface):
    """数据库管理器类"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """
        初始化数据库管理器
        
        Args:
            config_path: 数据库配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        self._connection_pool = Queue(maxsize=self.config['mysql']['pool']['max_connections'])
        self._pool_lock = threading.Lock()
        self._init_connection_pool()
        
        # 批量处理队列
        self._batch_queue = Queue()
        self._batch_thread = None
        self._stop_batch_processing = False
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"数据库配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _create_connection(self) -> pymysql.Connection:
        """创建数据库连接"""
        db_config = self.config['database']['primary']
        conn_config = self.config['database']['connection']
        
        return pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['username'],
            password=db_config['password'],
            database=db_config['database'],
            charset=db_config['charset'],
            connect_timeout=conn_config['connect_timeout'],
            read_timeout=conn_config['read_timeout'],
            write_timeout=conn_config['write_timeout'],
            autocommit=conn_config['autocommit'],
            cursorclass=DictCursor
        )
    
    def _init_connection_pool(self):
        """初始化连接池"""
        min_connections = self.config['database']['pool']['min_connections']
        
        for _ in range(min_connections):
            try:
                conn = self._create_connection()
                self._connection_pool.put(conn)
            except Exception as e:
                self.logger.error(f"创建数据库连接失败: {e}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            # 尝试从连接池获取连接
            try:
                conn = self._connection_pool.get_nowait()
                # 检查连接是否有效
                conn.ping(reconnect=True)
            except Empty:
                # 连接池为空，创建新连接
                conn = self._create_connection()
            
            yield conn
            
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库操作错误: {e}")
            raise
        finally:
            if conn:
                try:
                    # 将连接放回连接池
                    if self._connection_pool.qsize() < self.config['database']['pool']['max_connections']:
                        self._connection_pool.put(conn)
                    else:
                        conn.close()
                except Exception as e:
                    self.logger.error(f"归还连接到连接池失败: {e}")
                    if conn:
                        conn.close()
    
    def insert_market_quote(self, symbol: str, quote_data: Dict[str, Any]) -> bool:
        """
        插入市场行情数据
        
        Args:
            symbol: 股票代码
            quote_data: 行情数据
            
        Returns:
            bool: 插入是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 插入主行情数据 - 动态构建SQL，只插入存在的字段
                quote_field_mapping = {
                    'time': 'time',
                    'timetag': 'timetag',
                    'lastPrice': 'last_price',
                    'open': 'open_price',
                    'high': 'high_price',
                    'low': 'low_price',
                    'lastClose': 'last_close',
                    'amount': 'amount',
                    'volume': 'volume',
                    'pvolume': 'pvolume',
                    'stockStatus': 'stock_status',
                    'openInt': 'open_int',
                    'settlementPrice': 'settlement_price',
                    'lastSettlementPrice': 'last_settlement_price'
                }

                # 构建存在的字段列表
                quote_fields = ['symbol']  # symbol是必需的
                quote_placeholders = ['%(symbol)s']
                update_clauses = []
                quote_params = {'symbol': symbol}

                for param_key, db_field in quote_field_mapping.items():
                    if param_key in quote_data and quote_data[param_key] is not None:
                        quote_fields.append(db_field)
                        quote_placeholders.append(f'%({param_key})s')
                        update_clauses.append(f'{db_field}=VALUES({db_field})')
                        quote_params[param_key] = quote_data[param_key]

                # 构建动态SQL
                if update_clauses:
                    quote_sql = f"""
                    INSERT INTO market_quotes (
                        {', '.join(quote_fields)}
                    ) VALUES (
                        {', '.join(quote_placeholders)}
                    ) ON DUPLICATE KEY UPDATE
                        {', '.join(update_clauses)},
                        updated_at=CURRENT_TIMESTAMP
                    """
                else:
                    # 如果没有可更新的字段，只插入不更新
                    quote_sql = f"""
                    INSERT IGNORE INTO market_quotes (
                        {', '.join(quote_fields)}
                    ) VALUES (
                        {', '.join(quote_placeholders)}
                    )
                    """

                cursor.execute(quote_sql, quote_params)
                
                # 插入盘口数据 - 只有当盘口数据存在时才插入
                has_depth_data = any(key in quote_data for key in ['askPrice', 'bidPrice', 'askVol', 'bidVol'])

                if has_depth_data:
                    depth_fields = ['symbol']
                    depth_placeholders = ['%(symbol)s']
                    depth_update_clauses = []
                    depth_params = {'symbol': symbol}

                    # 检查并添加基础字段
                    if 'time' in quote_data and quote_data['time'] is not None:
                        depth_fields.append('time')
                        depth_placeholders.append('%(time)s')
                        depth_update_clauses.append('time=VALUES(time)')
                        depth_params['time'] = quote_data['time']

                    if 'timetag' in quote_data and quote_data['timetag'] is not None:
                        depth_fields.append('timetag')
                        depth_placeholders.append('%(timetag)s')
                        depth_update_clauses.append('timetag=VALUES(timetag)')
                        depth_params['timetag'] = quote_data['timetag']

                    # 处理买卖盘数据
                    ask_prices = quote_data.get('askPrice', [])
                    bid_prices = quote_data.get('bidPrice', [])
                    ask_vols = quote_data.get('askVol', [])
                    bid_vols = quote_data.get('bidVol', [])

                    # 添加存在的价格和数量字段
                    for i in range(5):
                        # 卖价
                        if i < len(ask_prices) and ask_prices[i] is not None:
                            field_name = f'ask_price_{i+1}'
                            depth_fields.append(field_name)
                            depth_placeholders.append(f'%({field_name})s')
                            depth_update_clauses.append(f'{field_name}=VALUES({field_name})')
                            depth_params[field_name] = ask_prices[i]

                        # 买价
                        if i < len(bid_prices) and bid_prices[i] is not None:
                            field_name = f'bid_price_{i+1}'
                            depth_fields.append(field_name)
                            depth_placeholders.append(f'%({field_name})s')
                            depth_update_clauses.append(f'{field_name}=VALUES({field_name})')
                            depth_params[field_name] = bid_prices[i]

                        # 卖量
                        if i < len(ask_vols) and ask_vols[i] is not None:
                            field_name = f'ask_vol_{i+1}'
                            depth_fields.append(field_name)
                            depth_placeholders.append(f'%({field_name})s')
                            depth_update_clauses.append(f'{field_name}=VALUES({field_name})')
                            depth_params[field_name] = ask_vols[i]

                        # 买量
                        if i < len(bid_vols) and bid_vols[i] is not None:
                            field_name = f'bid_vol_{i+1}'
                            depth_fields.append(field_name)
                            depth_placeholders.append(f'%({field_name})s')
                            depth_update_clauses.append(f'{field_name}=VALUES({field_name})')
                            depth_params[field_name] = bid_vols[i]

                    # 只有当有实际的盘口字段时才执行插入
                    if len(depth_fields) > 1:  # 除了symbol之外还有其他字段
                        if depth_update_clauses:
                            depth_sql = f"""
                            INSERT INTO market_depth (
                                {', '.join(depth_fields)}
                            ) VALUES (
                                {', '.join(depth_placeholders)}
                            ) ON DUPLICATE KEY UPDATE
                                {', '.join(depth_update_clauses)}
                            """
                        else:
                            # 如果没有可更新的字段，只插入不更新
                            depth_sql = f"""
                            INSERT IGNORE INTO market_depth (
                                {', '.join(depth_fields)}
                            ) VALUES (
                                {', '.join(depth_placeholders)}
                            )
                            """

                        cursor.execute(depth_sql, depth_params)
                
                conn.commit()
                self.logger.debug(f"成功插入 {symbol} 的行情数据")
                return True
                
        except Exception as e:
            self.logger.error(f"插入行情数据失败 {symbol}: {e}")
            return False
    
    def log_processing_result(self, message_id: str, symbol: str, status: str,
                            error_message: str = None, raw_data: Dict = None) -> bool:
        """
        记录处理结果

        Args:
            message_id: 消息ID
            symbol: 股票代码
            status: 处理状态 ('SUCCESS', 'FAILED', 'RETRY')
            error_message: 错误信息
            raw_data: 原始数据

        Returns:
            bool: 记录是否成功
        """
        try:
            # 确保状态值符合数据库ENUM定义
            valid_statuses = {'SUCCESS', 'FAILED', 'RETRY'}
            if status not in valid_statuses:
                # 如果状态值不在允许范围内，根据是否有错误信息来判断
                if error_message:
                    status = 'FAILED'
                else:
                    status = 'SUCCESS'

            with self.get_connection() as conn:
                cursor = conn.cursor()

                sql = """
                INSERT INTO processing_log (
                    message_id, symbol, status, error_message, raw_data
                ) VALUES (
                    %s, %s, %s, %s, %s
                )
                """

                cursor.execute(sql, (
                    message_id,
                    symbol,
                    status,
                    error_message,
                    json.dumps(raw_data) if raw_data else None
                ))

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"记录处理日志失败: {e}")
            return False
    
    def get_latest_quotes(self, symbols: List[str] = None) -> List[Dict[str, Any]]:
        """
        获取最新行情数据
        
        Args:
            symbols: 股票代码列表，为None时获取所有
            
        Returns:
            List[Dict]: 行情数据列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if symbols:
                    placeholders = ','.join(['%s'] * len(symbols))
                    sql = f"SELECT * FROM latest_quotes WHERE symbol IN ({placeholders})"
                    cursor.execute(sql, symbols)
                else:
                    sql = "SELECT * FROM latest_quotes"
                    cursor.execute(sql)
                
                return cursor.fetchall()
                
        except Exception as e:
            self.logger.error(f"查询最新行情失败: {e}")
            return []
    
    def health_check(self) -> Dict[str, Any]:
        """
        数据库健康检查
        
        Returns:
            Dict: 健康状态信息
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查连接
                cursor.execute("SELECT 1")
                
                # 检查表状态
                cursor.execute("SHOW TABLE STATUS LIKE 'market_quotes'")
                table_status = cursor.fetchone()
                
                # 检查最新数据时间
                cursor.execute("SELECT MAX(created_at) as latest_time FROM market_quotes")
                latest_data = cursor.fetchone()
                
                return {
                    'status': 'healthy',
                    'connection': 'ok',
                    'table_rows': table_status.get('Rows', 0) if table_status else 0,
                    'latest_data_time': latest_data.get('latest_time') if latest_data else None,
                    'pool_size': self._connection_pool.qsize()
                }
                
        except Exception as e:
            self.logger.error(f"数据库健康检查失败: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def get_database_type(self) -> str:
        """获取数据库类型"""
        return 'mysql'

    def get_pool_size(self) -> int:
        """获取连接池大小"""
        try:
            return self._connection_pool.qsize()
        except Exception:
            return 0

    def close(self):
        """关闭数据库连接池"""
        self._stop_batch_processing = True

        # 关闭所有连接
        while not self._connection_pool.empty():
            try:
                conn = self._connection_pool.get_nowait()
                conn.close()
            except Empty:
                break
            except Exception as e:
                self.logger.error(f"关闭数据库连接失败: {e}")


# 全局数据库管理器实例
_db_manager = None


def get_database_manager(config_path: str = "config/database.yaml") -> DatabaseManager:
    """获取数据库管理器单例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager(config_path)
    return _db_manager


def close_database_manager():
    """关闭数据库管理器"""
    global _db_manager
    if _db_manager:
        _db_manager.close()
        _db_manager = None
