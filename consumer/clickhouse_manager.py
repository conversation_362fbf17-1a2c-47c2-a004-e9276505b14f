"""
ClickHouse数据库管理器
用于处理市场数据的存储和查询
"""

import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import yaml
import json
from contextlib import contextmanager
import threading
from queue import Queue, Empty

try:
    from clickhouse_driver import Client
    from clickhouse_driver.errors import Error as ClickHouseError
except ImportError:
    raise ImportError("请安装clickhouse-driver: pip install clickhouse-driver")

from database_interface import DatabaseInterface


class ClickHouseManager(DatabaseInterface):
    """ClickHouse数据库管理器类"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """
        初始化ClickHouse数据库管理器
        
        Args:
            config_path: 数据库配置文件路径
        """
        super().__init__(config_path)
        self.config = self._load_config(config_path)
        self._connection_pool = Queue(maxsize=self.config['clickhouse']['pool']['max_connections'])
        self._pool_lock = threading.Lock()
        self._init_connection_pool()
        
        # 批量处理配置
        batch_config = self.config.get('clickhouse', {}).get('batch', {})
        self.batch_size = batch_config.get('size', 1000)
        self.batch_timeout = batch_config.get('timeout', 5.0)  # 秒
        self.batch_max_memory = batch_config.get('max_memory_mb', 100) * 1024 * 1024  # 字节
        self.batch_auto_flush = batch_config.get('auto_flush', True)
        self.batch_compression = batch_config.get('compression', True)
        self.batch_parallel_inserts = batch_config.get('parallel_inserts', 2)

        # 批量数据缓存
        self._batch_quotes = []
        self._batch_depth = []
        self._batch_logs = []
        self._batch_lock = threading.Lock()
        self._last_flush_time = time.time()

        # 批量插入统计
        self._batch_stats = {
            'total_batches': 0,
            'total_records': 0,
            'total_bytes': 0,
            'avg_batch_time': 0.0,
            'last_batch_time': None
        }
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"数据库配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _create_connection(self) -> Client:
        """创建ClickHouse连接"""
        ch_config = self.config['clickhouse']['primary']
        
        return Client(
            host=ch_config['host'],
            port=ch_config['port'],
            user=ch_config['username'],
            password=ch_config['password'],
            database=ch_config['database'],
            connect_timeout=ch_config.get('connect_timeout', 10),
            send_receive_timeout=ch_config.get('send_receive_timeout', 300),
            compression=ch_config.get('compression', False)
        )
    
    def _init_connection_pool(self):
        """初始化连接池"""
        min_connections = self.config['clickhouse']['pool']['min_connections']
        
        for _ in range(min_connections):
            try:
                client = self._create_connection()
                # 测试连接
                client.execute("SELECT 1")
                self._connection_pool.put(client)
            except Exception as e:
                self.logger.error(f"创建ClickHouse连接失败: {e}")
    
    @contextmanager
    def get_connection(self):
        """获取ClickHouse连接（上下文管理器）"""
        client = None
        try:
            # 尝试从连接池获取连接
            try:
                client = self._connection_pool.get_nowait()
                # 测试连接是否有效
                client.execute("SELECT 1")
            except Empty:
                # 连接池为空，创建新连接
                client = self._create_connection()
            except Exception:
                # 连接无效，创建新连接
                client = self._create_connection()
            
            yield client
            
        except Exception as e:
            self.logger.error(f"ClickHouse操作错误: {e}")
            raise
        finally:
            if client:
                try:
                    # 将连接放回连接池
                    if self._connection_pool.qsize() < self.config['clickhouse']['pool']['max_connections']:
                        self._connection_pool.put(client)
                    else:
                        client.disconnect()
                except Exception as e:
                    self.logger.error(f"归还连接到连接池失败: {e}")
                    try:
                        client.disconnect()
                    except:
                        pass
    
    def insert_market_quote(self, symbol: str, quote_data: Dict[str, Any]) -> bool:
        """
        插入市场行情数据
        
        Args:
            symbol: 股票代码
            quote_data: 行情数据
            
        Returns:
            bool: 插入是否成功
        """
        try:
            with self.get_connection() as client:
                # 准备主行情数据
                quote_row = self._prepare_quote_data(symbol, quote_data)
                if quote_row:
                    client.execute(
                        "INSERT INTO market_quotes VALUES",
                        [quote_row]
                    )
                
                # 准备盘口数据
                depth_row = self._prepare_depth_data(symbol, quote_data)
                if depth_row:
                    client.execute(
                        "INSERT INTO market_depth VALUES",
                        [depth_row]
                    )
                
                self.logger.debug(f"成功插入 {symbol} 的行情数据到ClickHouse")
                return True
                
        except Exception as e:
            self.logger.error(f"插入行情数据到ClickHouse失败 {symbol}: {e}")
            return False
    
    def _prepare_quote_data(self, symbol: str, quote_data: Dict[str, Any]) -> Optional[tuple]:
        """准备主行情数据"""
        try:
            # 转换时间戳
            timestamp = quote_data.get('time')
            if timestamp:
                # 如果是毫秒时间戳，转换为秒
                if timestamp > 1e10:
                    timestamp = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp)
            else:
                dt = datetime.now()
            
            return (
                symbol,
                dt,
                quote_data.get('time', 0),
                quote_data.get('timetag', ''),
                quote_data.get('lastPrice'),
                quote_data.get('open'),
                quote_data.get('high'),
                quote_data.get('low'),
                quote_data.get('lastClose'),
                quote_data.get('amount'),
                quote_data.get('volume'),
                quote_data.get('pvolume'),
                quote_data.get('stockStatus'),
                quote_data.get('openInt'),
                quote_data.get('settlementPrice'),
                quote_data.get('lastSettlementPrice'),
                datetime.now()  # created_at
            )
        except Exception as e:
            self.logger.error(f"准备行情数据失败: {e}")
            return None
    
    def _prepare_depth_data(self, symbol: str, quote_data: Dict[str, Any]) -> Optional[tuple]:
        """准备盘口数据"""
        try:
            # 检查是否有盘口数据
            has_depth = any(key in quote_data for key in ['askPrice', 'bidPrice', 'askVol', 'bidVol'])
            if not has_depth:
                return None
            
            # 转换时间戳
            timestamp = quote_data.get('time')
            if timestamp:
                if timestamp > 1e10:
                    timestamp = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp)
            else:
                dt = datetime.now()
            
            # 获取盘口数据
            ask_prices = quote_data.get('askPrice', [None] * 5)
            bid_prices = quote_data.get('bidPrice', [None] * 5)
            ask_vols = quote_data.get('askVol', [None] * 5)
            bid_vols = quote_data.get('bidVol', [None] * 5)
            
            # 确保数组长度为5
            ask_prices = (ask_prices + [None] * 5)[:5]
            bid_prices = (bid_prices + [None] * 5)[:5]
            ask_vols = (ask_vols + [None] * 5)[:5]
            bid_vols = (bid_vols + [None] * 5)[:5]
            
            return (
                symbol,
                dt,
                quote_data.get('time', 0),
                quote_data.get('timetag', ''),
                *ask_prices,  # ask_price_1 到 ask_price_5
                *bid_prices,  # bid_price_1 到 bid_price_5
                *ask_vols,    # ask_vol_1 到 ask_vol_5
                *bid_vols,    # bid_vol_1 到 bid_vol_5
                datetime.now()  # created_at
            )
        except Exception as e:
            self.logger.error(f"准备盘口数据失败: {e}")
            return None
    
    def log_processing_result(self, message_id: str, symbol: str, status: str,
                            error_message: str = None, raw_data: Dict = None) -> bool:
        """
        记录处理结果
        
        Args:
            message_id: 消息ID
            symbol: 股票代码
            status: 处理状态
            error_message: 错误信息
            raw_data: 原始数据
            
        Returns:
            bool: 记录是否成功
        """
        try:
            # 确保状态值有效
            valid_statuses = {'SUCCESS', 'FAILED', 'RETRY'}
            if status not in valid_statuses:
                if error_message:
                    status = 'FAILED'
                else:
                    status = 'SUCCESS'
            
            with self.get_connection() as client:
                log_row = (
                    message_id,
                    symbol,
                    datetime.now(),
                    status,
                    error_message,
                    json.dumps(raw_data) if raw_data else None
                )
                
                client.execute(
                    "INSERT INTO processing_log VALUES",
                    [log_row]
                )
                
                return True
                
        except Exception as e:
            self.logger.error(f"记录处理日志到ClickHouse失败: {e}")
            return False
    
    def get_latest_quotes(self, symbols: List[str] = None) -> List[Dict[str, Any]]:
        """
        获取最新行情数据
        
        Args:
            symbols: 股票代码列表，为None时获取所有
            
        Returns:
            List[Dict]: 行情数据列表
        """
        try:
            with self.get_connection() as client:
                if symbols:
                    placeholders = ','.join([f"'{symbol}'" for symbol in symbols])
                    sql = f"""
                    SELECT * FROM (
                        SELECT *, ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY created_at DESC) as rn
                        FROM market_quotes
                        WHERE symbol IN ({placeholders})
                    ) WHERE rn = 1
                    """
                else:
                    sql = """
                    SELECT * FROM (
                        SELECT *, ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY created_at DESC) as rn
                        FROM market_quotes
                    ) WHERE rn = 1
                    """
                
                result = client.execute(sql)
                
                # 转换为字典列表
                columns = [
                    'symbol', 'time_dt', 'time', 'timetag', 'last_price', 'open_price',
                    'high_price', 'low_price', 'last_close', 'amount', 'volume',
                    'pvolume', 'stock_status', 'open_int', 'settlement_price',
                    'last_settlement_price', 'created_at', 'rn'
                ]
                
                return [dict(zip(columns, row)) for row in result]
                
        except Exception as e:
            self.logger.error(f"查询最新行情失败: {e}")
            return []
    
    def health_check(self) -> Dict[str, Any]:
        """
        数据库健康检查
        
        Returns:
            Dict: 健康状态信息
        """
        try:
            with self.get_connection() as client:
                # 检查连接
                client.execute("SELECT 1")
                
                # 检查表状态
                tables_info = client.execute("""
                    SELECT table, total_rows, total_bytes
                    FROM system.tables
                    WHERE database = currentDatabase()
                    AND table IN ('market_quotes', 'market_depth', 'processing_log')
                """)
                
                # 检查最新数据时间
                latest_data = client.execute("""
                    SELECT MAX(created_at) as latest_time
                    FROM market_quotes
                """)
                
                return {
                    'status': 'healthy',
                    'connection': 'ok',
                    'database_type': 'clickhouse',
                    'tables_info': {row[0]: {'rows': row[1], 'bytes': row[2]} for row in tables_info},
                    'latest_data_time': latest_data[0][0] if latest_data and latest_data[0][0] else None,
                    'pool_size': self._connection_pool.qsize()
                }
                
        except Exception as e:
            self.logger.error(f"ClickHouse健康检查失败: {e}")
            return {
                'status': 'unhealthy',
                'database_type': 'clickhouse',
                'error': str(e)
            }
    
    def get_database_type(self) -> str:
        """获取数据库类型"""
        return 'clickhouse'

    def get_pool_size(self) -> int:
        """获取连接池大小"""
        try:
            return self._connection_pool.qsize()
        except Exception:
            return 0

    def get_stats(self) -> Dict[str, Any]:
        """获取ClickHouse统计信息"""
        base_stats = super().get_stats()

        # 添加批量插入统计
        batch_stats = self.get_batch_stats()
        base_stats.update({
            'batch_insert': batch_stats,
            'clickhouse_specific': {
                'compression_enabled': self.batch_compression,
                'parallel_inserts': self.batch_parallel_inserts,
                'auto_flush': self.batch_auto_flush
            }
        })

        return base_stats

    def optimize_tables(self) -> bool:
        """优化ClickHouse表"""
        try:
            with self.get_connection() as client:
                tables = ['market_quotes', 'market_depth', 'processing_log']

                for table in tables:
                    try:
                        client.execute(f"OPTIMIZE TABLE {table}")
                        self.logger.info(f"优化表 {table} 完成")
                    except Exception as e:
                        self.logger.warning(f"优化表 {table} 失败: {e}")

                return True

        except Exception as e:
            self.logger.error(f"优化表失败: {e}")
            return False

    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        try:
            with self.get_connection() as client:
                # 获取表大小
                size_query = f"""
                SELECT
                    formatReadableSize(sum(bytes)) as size,
                    sum(rows) as rows,
                    count() as parts
                FROM system.parts
                WHERE table = '{table_name}' AND active = 1
                """

                result = client.execute(size_query)
                if result:
                    size, rows, parts = result[0]
                    return {
                        'table': table_name,
                        'size': size,
                        'rows': rows,
                        'parts': parts
                    }

        except Exception as e:
            self.logger.error(f"获取表 {table_name} 信息失败: {e}")

        return {}

    def analyze_performance(self) -> Dict[str, Any]:
        """分析ClickHouse性能"""
        try:
            with self.get_connection() as client:
                # 获取查询统计
                query_stats = client.execute("""
                SELECT
                    query_kind,
                    count() as count,
                    avg(query_duration_ms) as avg_duration,
                    max(query_duration_ms) as max_duration
                FROM system.query_log
                WHERE event_date = today()
                GROUP BY query_kind
                ORDER BY count DESC
                """)

                # 获取内存使用
                memory_stats = client.execute("""
                SELECT
                    formatReadableSize(sum(memory_usage)) as memory_usage
                FROM system.processes
                """)

                return {
                    'query_stats': query_stats,
                    'memory_usage': memory_stats[0][0] if memory_stats else '0 B',
                    'batch_stats': self.get_batch_stats()
                }

        except Exception as e:
            self.logger.error(f"分析性能失败: {e}")
            return {}

    def cleanup_old_data(self, days: int = 30) -> bool:
        """清理旧数据"""
        try:
            with self.get_connection() as client:
                cutoff_date = f"today() - {days}"

                tables_config = {
                    'market_quotes': 'timestamp',
                    'market_depth': 'timestamp',
                    'processing_log': 'created_at'
                }

                for table, date_column in tables_config.items():
                    try:
                        query = f"ALTER TABLE {table} DELETE WHERE {date_column} < {cutoff_date}"
                        client.execute(query)
                        self.logger.info(f"清理表 {table} 中 {days} 天前的数据")
                    except Exception as e:
                        self.logger.warning(f"清理表 {table} 失败: {e}")

                return True

        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接池"""
        # 关闭所有连接
        while not self._connection_pool.empty():
            try:
                client = self._connection_pool.get_nowait()
                client.disconnect()
            except Empty:
                break
            except Exception as e:
                self.logger.error(f"关闭ClickHouse连接失败: {e}")
    
    def batch_insert_market_quotes(self, quotes: List[Dict[str, Any]]) -> bool:
        """
        批量插入行情数据（ClickHouse优化版本）

        Args:
            quotes: 行情数据列表

        Returns:
            bool: 批量插入是否成功
        """
        if self.batch_auto_flush:
            return self._batch_insert_with_buffer(quotes)
        else:
            return self._direct_batch_insert(quotes)

    def _direct_batch_insert(self, quotes: List[Dict[str, Any]]) -> bool:
        """直接批量插入（不使用缓冲）"""
        try:
            start_time = time.time()

            with self.get_connection() as client:
                # 准备批量数据
                quote_rows = []
                depth_rows = []

                for quote in quotes:
                    symbol = quote.get('symbol')
                    quote_data = quote.get('quote_data')

                    if symbol and quote_data:
                        # 准备行情数据
                        quote_row = self._prepare_quote_data(symbol, quote_data)
                        if quote_row:
                            quote_rows.append(quote_row)

                        # 准备盘口数据
                        depth_row = self._prepare_depth_data(symbol, quote_data)
                        if depth_row:
                            depth_rows.append(depth_row)

                # 执行批量插入
                success = self._execute_batch_insert(client, quote_rows, depth_rows)

                # 更新统计信息
                if success:
                    self._update_batch_stats(len(quote_rows) + len(depth_rows), time.time() - start_time)

                return success

        except Exception as e:
            self.logger.error(f"直接批量插入失败: {e}")
            return False

    def _batch_insert_with_buffer(self, quotes: List[Dict[str, Any]]) -> bool:
        """使用缓冲区的批量插入"""
        with self._batch_lock:
            # 添加到缓冲区
            for quote in quotes:
                symbol = quote.get('symbol')
                quote_data = quote.get('quote_data')

                if symbol and quote_data:
                    # 准备行情数据
                    quote_row = self._prepare_quote_data(symbol, quote_data)
                    if quote_row:
                        self._batch_quotes.append(quote_row)

                    # 准备盘口数据
                    depth_row = self._prepare_depth_data(symbol, quote_data)
                    if depth_row:
                        self._batch_depth.append(depth_row)

            # 检查是否需要刷新
            should_flush = self._should_flush_batch()

            if should_flush:
                return self._flush_batch()

            return True

    def _should_flush_batch(self) -> bool:
        """检查是否应该刷新批次"""
        # 检查数量
        total_records = len(self._batch_quotes) + len(self._batch_depth) + len(self._batch_logs)
        if total_records >= self.batch_size:
            return True

        # 检查时间
        if time.time() - self._last_flush_time >= self.batch_timeout:
            return True

        # 检查内存使用（估算）
        estimated_memory = total_records * 200  # 估算每条记录200字节
        if estimated_memory >= self.batch_max_memory:
            return True

        return False

    def _flush_batch(self) -> bool:
        """刷新批次数据"""
        if not self._batch_quotes and not self._batch_depth and not self._batch_logs:
            return True

        try:
            start_time = time.time()

            with self.get_connection() as client:
                # 复制并清空缓冲区
                quote_rows = self._batch_quotes.copy()
                depth_rows = self._batch_depth.copy()
                log_rows = self._batch_logs.copy()

                self._batch_quotes.clear()
                self._batch_depth.clear()
                self._batch_logs.clear()
                self._last_flush_time = time.time()

                # 执行批量插入
                success = self._execute_batch_insert(client, quote_rows, depth_rows, log_rows)

                # 更新统计信息
                if success:
                    total_records = len(quote_rows) + len(depth_rows) + len(log_rows)
                    self._update_batch_stats(total_records, time.time() - start_time)

                return success

        except Exception as e:
            self.logger.error(f"刷新批次失败: {e}")
            return False

    def _execute_batch_insert(self, client: Client, quote_rows: List[tuple],
                            depth_rows: List[tuple], log_rows: List[tuple] = None) -> bool:
        """执行批量插入操作"""
        try:
            # 设置插入参数
            insert_settings = {}
            if self.batch_compression:
                insert_settings['compress'] = 1

            # 批量插入行情数据
            if quote_rows:
                if self.batch_parallel_inserts > 1 and len(quote_rows) > 100:
                    self._parallel_insert(client, "market_quotes", quote_rows, insert_settings)
                else:
                    client.execute(
                        "INSERT INTO market_quotes VALUES",
                        quote_rows,
                        settings=insert_settings
                    )
                self.logger.debug(f"插入 {len(quote_rows)} 条行情记录")

            # 批量插入盘口数据
            if depth_rows:
                if self.batch_parallel_inserts > 1 and len(depth_rows) > 100:
                    self._parallel_insert(client, "market_depth", depth_rows, insert_settings)
                else:
                    client.execute(
                        "INSERT INTO market_depth VALUES",
                        depth_rows,
                        settings=insert_settings
                    )
                self.logger.debug(f"插入 {len(depth_rows)} 条盘口记录")

            # 批量插入日志数据
            if log_rows:
                client.execute(
                    "INSERT INTO processing_log VALUES",
                    log_rows,
                    settings=insert_settings
                )
                self.logger.debug(f"插入 {len(log_rows)} 条日志记录")

            return True

        except Exception as e:
            self.logger.error(f"执行批量插入失败: {e}")
            return False

    def _parallel_insert(self, client: Client, table: str, rows: List[tuple], settings: dict):
        """并行插入大批量数据"""
        import threading
        from concurrent.futures import ThreadPoolExecutor, as_completed

        # 分割数据
        chunk_size = len(rows) // self.batch_parallel_inserts
        chunks = [rows[i:i + chunk_size] for i in range(0, len(rows), chunk_size)]

        def insert_chunk(chunk):
            try:
                with self.get_connection() as chunk_client:
                    chunk_client.execute(f"INSERT INTO {table} VALUES", chunk, settings=settings)
                return len(chunk)
            except Exception as e:
                self.logger.error(f"并行插入块失败: {e}")
                return 0

        # 并行执行
        with ThreadPoolExecutor(max_workers=self.batch_parallel_inserts) as executor:
            futures = [executor.submit(insert_chunk, chunk) for chunk in chunks]
            total_inserted = sum(future.result() for future in as_completed(futures))

        self.logger.debug(f"并行插入完成: {total_inserted}/{len(rows)} 记录")

    def _update_batch_stats(self, record_count: int, batch_time: float):
        """更新批量插入统计信息"""
        self._batch_stats['total_batches'] += 1
        self._batch_stats['total_records'] += record_count
        self._batch_stats['total_bytes'] += record_count * 200  # 估算

        # 更新平均时间
        if self._batch_stats['avg_batch_time'] == 0:
            self._batch_stats['avg_batch_time'] = batch_time
        else:
            self._batch_stats['avg_batch_time'] = (
                self._batch_stats['avg_batch_time'] * 0.9 + batch_time * 0.1
            )

        self._batch_stats['last_batch_time'] = time.time()

    def get_batch_stats(self) -> Dict[str, Any]:
        """获取批量插入统计信息"""
        with self._batch_lock:
            stats = self._batch_stats.copy()
            stats.update({
                'current_buffer_size': {
                    'quotes': len(self._batch_quotes),
                    'depth': len(self._batch_depth),
                    'logs': len(self._batch_logs)
                },
                'batch_config': {
                    'batch_size': self.batch_size,
                    'batch_timeout': self.batch_timeout,
                    'auto_flush': self.batch_auto_flush,
                    'compression': self.batch_compression,
                    'parallel_inserts': self.batch_parallel_inserts
                }
            })
            return stats

    def force_flush(self) -> bool:
        """强制刷新所有缓冲的数据"""
        with self._batch_lock:
            return self._flush_batch()
