"""
RabbitMQ消费者模块
用于消费市场数据消息并存储到数据库
"""

import json
import logging
import time
import uuid
from typing import Dict, Any, Callable
import yaml
import pika
from pika.exceptions import AMQPConnectionError, AMQPChannelError
import threading
from datetime import datetime

from database_factory import get_database_manager


class RabbitMQConsumer:
    """RabbitMQ消费者类"""
    
    def __init__(self, config_path: str = "config.yaml", db_config_path: str = "config/database.yaml"):
        """
        初始化RabbitMQ消费者
        
        Args:
            config_path: RabbitMQ配置文件路径
            db_config_path: 数据库配置文件路径
        """
        self.config = self._load_config(config_path)
        self.db_manager = get_database_manager(db_config_path)
        self.logger = self._setup_logger()
        
        self.connection = None
        self.channel = None
        self.consuming = False
        self._consumer_tag = None
        
        # 统计信息
        self.stats = {
            'messages_received': 0,
            'messages_processed': 0,
            'messages_failed': 0,
            'start_time': None
        }

        # 批量处理配置
        consumer_config = self.config.get('consumer', {})
        self.enable_batch_processing = consumer_config.get('batch', {}).get('enabled', False)
        self.batch_size = consumer_config.get('batch', {}).get('size', 50)
        self.batch_timeout = consumer_config.get('batch', {}).get('timeout_seconds', 5)

        # 批量处理缓存
        self._batch_buffer = []
        self._last_batch_time = time.time()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            # 确保日志目录存在
            import os
            from pathlib import Path

            log_dir = Path(__file__).parent.parent / 'logs'
            log_dir.mkdir(exist_ok=True)
            log_file = log_dir / 'consumer.log'

            # 创建格式器
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

            # 文件处理器 - 使用UTF-8编码
            try:
                file_handler = logging.FileHandler(
                    log_file,
                    mode='a',
                    encoding='utf-8'
                )
                file_handler.setFormatter(file_formatter)
                logger.addHandler(file_handler)
            except Exception as e:
                print(f"配置消费者日志文件失败: {e}")

            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(file_formatter)
            logger.addHandler(console_handler)

        return logger
    
    def connect(self) -> bool:
        """连接到RabbitMQ服务器"""
        max_retries = 3
        retry_delay = 5

        for attempt in range(max_retries):
            try:
                rabbitmq_config = self.config['rabbitmq']

                # 创建连接参数 - 增加稳定性配置
                credentials = pika.PlainCredentials(
                    rabbitmq_config['username'],
                    rabbitmq_config['password']
                )

                parameters = pika.ConnectionParameters(
                    host=rabbitmq_config['host'],
                    port=rabbitmq_config['port'],
                    virtual_host=rabbitmq_config['virtual_host'],
                    credentials=credentials,
                    heartbeat=300,  # 5分钟心跳，更稳定
                    blocked_connection_timeout=300,
                    socket_timeout=30,
                    connection_attempts=3,
                    retry_delay=2,
                    # 添加TCP keepalive
                    tcp_options={
                        'TCP_KEEPIDLE': 600,
                        'TCP_KEEPINTVL': 30,
                        'TCP_KEEPCNT': 3
                    }
                )

                # 建立连接
                self.connection = pika.BlockingConnection(parameters)
                self.channel = self.connection.channel()

                # 设置QoS - 单线程使用较小的预取
                prefetch_count = 5  # 单线程保守预取
                self.channel.basic_qos(prefetch_count=prefetch_count, global_qos=False)

                # 声明队列确保存在
                queue_name = rabbitmq_config['queue']['name']
                self.channel.queue_declare(
                    queue=queue_name,
                    durable=rabbitmq_config['queue'].get('durable', True),
                    auto_delete=rabbitmq_config['queue'].get('auto_delete', False)
                )

                self.logger.info(f"成功连接到RabbitMQ服务器 (尝试 {attempt + 1}/{max_retries})")
                self.logger.info(f"预取数量: {prefetch_count}, 队列: {queue_name}")
                return True

            except AMQPConnectionError as e:
                self.logger.error(f"连接RabbitMQ失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    self.logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
            except Exception as e:
                self.logger.error(f"连接过程中发生错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2

        self.logger.error("所有连接尝试都失败了")
        return False

    def _reconnect(self) -> bool:
        """重新连接到RabbitMQ"""
        self.logger.info("尝试重新连接到RabbitMQ...")

        # 关闭现有连接
        try:
            if self.channel and not self.channel.is_closed:
                self.channel.close()
        except:
            pass

        try:
            if self.connection and not self.connection.is_closed:
                self.connection.close()
        except:
            pass

        # 重置连接对象
        self.connection = None
        self.channel = None
        self.consuming = False

        # 等待一段时间再重连
        time.sleep(5)

        # 尝试重新连接
        if self.connect():
            self.logger.info("重新连接成功")
            return True
        else:
            self.logger.error("重新连接失败")
            return False

    def disconnect(self):
        """断开连接"""
        try:
            if self.consuming:
                self.stop_consuming()
            
            if self.connection and not self.connection.is_closed:
                self.connection.close()
                self.logger.info("已断开RabbitMQ连接")
        except Exception as e:
            self.logger.error(f"断开连接时发生错误: {e}")
    
    def _process_message(self, channel, method, properties, body) -> bool:
        """
        处理接收到的消息
        
        Args:
            channel: 通道
            method: 方法
            properties: 属性
            body: 消息体
            
        Returns:
            bool: 处理是否成功
        """
        message_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            self.stats['messages_received'] += 1
            
            # 解析消息
            message_data = json.loads(body.decode('utf-8'))
            self.logger.debug(f"接收到消息: {message_id}")
            
            # 提取数据
            if 'data' in message_data:
                market_data = message_data['data']
            else:
                market_data = message_data
            
            # 检查是否启用批量处理
            if self.enable_batch_processing:
                # 使用批量处理
                success_count, total_count = self._process_market_data_batch(market_data)
            else:
                # 使用单条处理
                success_count, total_count = self._process_market_data_single(market_data)
            
            # 记录处理结果
            if success_count == total_count:
                status = 'SUCCESS'
                self.stats['messages_processed'] += 1
            else:
                # 部分成功的情况记录为FAILED，但在错误信息中说明是部分成功
                status = 'FAILED'
                self.stats['messages_failed'] += 1

            # 统计股票数量
            symbol_count = len(market_data)

            # 记录处理日志
            self.db_manager.log_processing_result(
                message_id=message_id,
                symbol=f"BATCH_{symbol_count}_SYMBOLS_{total_count}_RECORDS",
                status=status,
                error_message=None if success_count == total_count else f"部分失败: {success_count}/{total_count}",
                raw_data=market_data
            )

            processing_time = time.time() - start_time
            self.logger.info(f"消息处理完成: {message_id}, 股票数: {symbol_count}, 记录数: {success_count}/{total_count}, 耗时: {processing_time:.3f}s")
            
            # 确认消息
            channel.basic_ack(delivery_tag=method.delivery_tag)
            return True
            
        except json.JSONDecodeError as e:
            self.logger.error(f"消息格式错误 {message_id}: {e}")
            self.stats['messages_failed'] += 1
            
            # 记录错误
            self.db_manager.log_processing_result(
                message_id=message_id,
                symbol="UNKNOWN",
                status='FAILED',
                error_message=f"JSON解析错误: {e}",
                raw_data={'raw_body': body.decode('utf-8', errors='ignore')}
            )
            
            # 拒绝消息（不重新入队）
            channel.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
            return False
            
        except Exception as e:
            self.logger.error(f"处理消息时发生未知错误 {message_id}: {e}")
            self.stats['messages_failed'] += 1
            
            # 记录错误
            self.db_manager.log_processing_result(
                message_id=message_id,
                symbol="UNKNOWN",
                status='FAILED',
                error_message=str(e),
                raw_data={'raw_body': body.decode('utf-8', errors='ignore')}
            )
            
            # 拒绝消息并重新入队（可能是临时错误）
            channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
            return False

    def _process_market_data_single(self, market_data) -> tuple:
        """单条处理市场数据"""
        success_count = 0
        total_count = 0

        for symbol, quote_data_list in market_data.items():
            # 新数据格式：每个股票对应一个数组
            if not isinstance(quote_data_list, list):
                self.logger.warning(f"股票 {symbol} 的数据格式不正确，期望数组格式")
                continue

            # 处理数组中的每个行情数据
            for quote_data in quote_data_list:
                total_count += 1

                try:
                    # 插入数据库
                    if self.db_manager.insert_market_quote(symbol, quote_data):
                        success_count += 1
                        self.logger.debug(f"成功处理 {symbol} 的数据 (时间: {quote_data.get('time', 'N/A')})")
                    else:
                        self.logger.warning(f"处理 {symbol} 的数据失败")

                except Exception as e:
                    self.logger.error(f"处理 {symbol} 数据时发生错误: {e}")

        return success_count, total_count

    def _process_market_data_batch(self, market_data) -> tuple:
        """批量处理市场数据"""
        try:
            # 准备批量数据
            batch_quotes = []
            total_count = 0

            for symbol, quote_data_list in market_data.items():
                if not isinstance(quote_data_list, list):
                    self.logger.warning(f"股票 {symbol} 的数据格式不正确，期望数组格式")
                    continue

                # 转换为批量格式
                for quote_data in quote_data_list:
                    total_count += 1
                    batch_quotes.append({
                        'symbol': symbol,
                        'quote_data': quote_data
                    })

            # 批量插入数据库
            if batch_quotes:
                if self.db_manager.batch_insert_market_quotes(batch_quotes):
                    success_count = total_count
                    self.logger.debug(f"批量处理成功: {len(batch_quotes)} 条记录")
                else:
                    success_count = 0
                    self.logger.warning(f"批量处理失败: {len(batch_quotes)} 条记录")
            else:
                success_count = 0

            return success_count, total_count

        except Exception as e:
            self.logger.error(f"批量处理市场数据失败: {e}")
            return 0, 0

    def start_consuming(self):
        """开始消费消息"""
        max_reconnect_attempts = 5
        reconnect_count = 0

        while reconnect_count < max_reconnect_attempts:
            try:
                # 确保连接正常
                if not self.connection or self.connection.is_closed:
                    if not self.connect():
                        raise Exception("无法连接到RabbitMQ服务器")

                queue_name = self.config['rabbitmq']['queue']['name']

                # 设置消费者
                self._consumer_tag = self.channel.basic_consume(
                    queue=queue_name,
                    on_message_callback=self._process_message
                )

                self.consuming = True
                if not self.stats['start_time']:
                    self.stats['start_time'] = datetime.now()

                self.logger.info(f"开始消费队列: {queue_name} (重连次数: {reconnect_count})")
                self.logger.info("等待消息... 按 CTRL+C 停止")

                # 重置重连计数
                reconnect_count = 0

                # 开始消费
                self.channel.start_consuming()

            except KeyboardInterrupt:
                self.logger.info("接收到中断信号，正在停止消费...")
                self.stop_consuming()
                break
            except Exception as e:
                self.logger.error(f"消费过程中发生错误: {e}")

                # 检查是否是连接相关错误
                error_str = str(e).lower()
                if any(keyword in error_str for keyword in ['connection', 'stream', 'socket', 'transport', 'channel']):
                    reconnect_count += 1
                    self.logger.warning(f"检测到连接错误，尝试重连 ({reconnect_count}/{max_reconnect_attempts})")

                    if reconnect_count < max_reconnect_attempts:
                        if self._reconnect():
                            continue  # 重连成功，继续消费
                        else:
                            self.logger.error("重连失败，等待后重试")
                            time.sleep(10)
                    else:
                        self.logger.error("达到最大重连次数，停止消费")
                        break
                else:
                    # 非连接错误，直接抛出
                    raise
    
    def stop_consuming(self):
        """停止消费消息"""
        if self.consuming and self.channel:
            try:
                self.channel.stop_consuming()
                self.consuming = False
                self.logger.info("已停止消费消息")
            except Exception as e:
                self.logger.error(f"停止消费时发生错误: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        if stats['start_time']:
            runtime = datetime.now() - stats['start_time']
            stats['runtime_seconds'] = runtime.total_seconds()
            
            if stats['runtime_seconds'] > 0:
                stats['messages_per_second'] = stats['messages_received'] / stats['runtime_seconds']
            else:
                stats['messages_per_second'] = 0
        
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查RabbitMQ连接
            rabbitmq_status = 'healthy' if (self.connection and not self.connection.is_closed) else 'unhealthy'
            
            # 检查数据库连接
            db_health = self.db_manager.health_check()
            
            # 检查消费状态
            consumer_status = 'consuming' if self.consuming else 'stopped'
            
            return {
                'status': 'healthy' if rabbitmq_status == 'healthy' and db_health['status'] == 'healthy' else 'unhealthy',
                'rabbitmq': rabbitmq_status,
                'database': db_health,
                'consumer': consumer_status,
                'stats': self.get_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


# 全局消费者实例
_consumer = None


def get_consumer(config_path: str = "config.yaml", db_config_path: str = "config/database.yaml") -> RabbitMQConsumer:
    """获取消费者单例"""
    global _consumer
    if _consumer is None:
        _consumer = RabbitMQConsumer(config_path, db_config_path)
    return _consumer


def close_consumer():
    """关闭消费者"""
    global _consumer
    if _consumer:
        _consumer.disconnect()
        _consumer = None
