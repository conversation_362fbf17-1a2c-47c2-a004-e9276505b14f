# 市场数据消费者服务

这是一个完整的市场数据消费者服务，用于从RabbitMQ消费股票行情数据并存储到MySQL数据库中。服务集成了Prometheus监控和健康检查功能。

## 功能特性

- ✅ **RabbitMQ消息消费**: 从指定队列消费市场数据消息
- ✅ **MySQL数据存储**: 将行情数据存储到结构化的MySQL表中
- ✅ **Prometheus监控**: 暴露详细的业务和系统指标
- ✅ **健康检查**: 提供HTTP接口用于服务健康状态检测
- ✅ **连接池管理**: 高效的数据库连接池管理
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **日志记录**: 详细的日志记录和监控
- ✅ **优雅关闭**: 支持优雅关闭和资源清理

## 项目结构

```
consumer/
├── main.py                 # 主服务程序
├── database_manager.py     # 数据库管理器
├── rabbitmq_consumer.py    # RabbitMQ消费者
├── metrics.py              # Prometheus指标收集
├── health_check.py         # 健康检查服务
├── test_consumer.py        # 单元测试
├── run_tests.py           # 测试运行脚本
└── README.md              # 使用文档

config/
├── database.yaml          # 数据库配置
└── database_schema.sql    # 数据库表结构

logs/                      # 日志目录
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

### 1. RabbitMQ配置 (config.yaml)

```yaml
rabbitmq:
  host: "localhost"
  port: 5672
  username: "guest"
  password: "guest"
  virtual_host: "/"
  
  queue:
    name: "market_data_queue"
    durable: true
    
  exchange:
    name: "market_data_exchange"
    type: "direct"
    
  routing_key: "market.data"
```

### 2. 数据库配置 (config/database.yaml)

```yaml
database:
  primary:
    host: "localhost"
    port: 3306
    username: "root"
    password: "password"
    database: "market_data"
    charset: "utf8mb4"
    
  pool:
    min_connections: 5
    max_connections: 20
    max_idle_time: 300
    max_lifetime: 3600
```

## 数据库设置

1. **创建数据库和表**:
```bash
mysql -u root -p < config/database_schema.sql
```

2. **主要表结构**:
   - `stock_info`: 股票基本信息
   - `market_quotes`: 实时行情主表
   - `market_depth`: 买卖盘口数据
   - `processing_log`: 数据处理日志
   - `system_metrics`: 系统监控指标

## 使用方法

### 1. 启动服务

```bash
cd consumer
python main.py
```

### 2. 带参数启动

```bash
python main.py \
  --config ../config.yaml \
  --db-config ../config/database.yaml \
  --metrics-port 8000 \
  --health-port 8080 \
  --log-level INFO
```

### 3. 服务端点

启动后，服务将提供以下端点：

- **健康检查**: `http://localhost:8080/health`
- **详细健康检查**: `http://localhost:8080/health/detailed`
- **存活性检查**: `http://localhost:8080/health/live`
- **就绪性检查**: `http://localhost:8080/health/ready`
- **统计信息**: `http://localhost:8080/stats`
- **Prometheus指标**: `http://localhost:8000/metrics`

## 监控指标

### 业务指标

- `messages_received_total`: 接收的消息总数
- `messages_processed_total`: 处理成功的消息总数
- `messages_failed_total`: 处理失败的消息总数
- `message_processing_duration_seconds`: 消息处理耗时
- `stock_quotes_stored_total`: 存储的股票行情总数
- `data_lag_seconds`: 数据延迟（秒）

### 系统指标

- `database_operations_total`: 数据库操作总数
- `database_connection_pool_size`: 数据库连接池大小
- `rabbitmq_connection_status`: RabbitMQ连接状态
- `system_uptime_seconds`: 系统运行时间
- `active_threads`: 活跃线程数

## 消息格式

服务期望接收以下格式的消息：

```json
{
  "timestamp": "2025-06-19T15:00:03",
  "data": {
    "600000.SH": {
      "time": 1750316403000,
      "timetag": "20250619 15:00:03",
      "lastPrice": 12.74,
      "open": 12.78,
      "high": 12.84,
      "low": 12.64,
      "lastClose": 12.78,
      "amount": 557599600,
      "volume": 437681,
      "pvolume": 43768050,
      "stockStatus": 5,
      "openInt": 15,
      "settlementPrice": 0,
      "lastSettlementPrice": 12.78,
      "askPrice": [0, 0, 0, 0, 0],
      "bidPrice": [0, 0, 0, 0, 0],
      "askVol": [0, 0, 0, 0, 0],
      "bidVol": [0, 0, 0, 0, 0]
    }
  }
}
```

## 测试

### 运行所有测试

```bash
cd consumer
python run_tests.py
```

### 安装依赖并运行测试

```bash
python run_tests.py --install
```

### 运行特定测试

```bash
python run_tests.py --test TestDatabaseManager
```

### 查看测试覆盖率

测试完成后，HTML覆盖率报告将生成在 `htmlcov/index.html`

## 部署建议

### 1. Docker部署

创建 `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080 8000

CMD ["python", "consumer/main.py"]
```

### 2. 环境变量配置

支持通过环境变量覆盖配置：

```bash
export RABBITMQ_HOST=rabbitmq-server
export MYSQL_HOST=mysql-server
export MYSQL_PASSWORD=secure_password
```

### 3. 监控集成

#### Prometheus配置

```yaml
scrape_configs:
  - job_name: 'market-data-consumer'
    static_configs:
      - targets: ['localhost:8000']
```

#### Grafana仪表板

导入提供的Grafana仪表板模板来可视化指标。

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数
   - 检查网络连接

2. **RabbitMQ连接失败**
   - 确认RabbitMQ服务状态
   - 验证用户名密码
   - 检查队列是否存在

3. **消息处理失败**
   - 查看日志文件
   - 检查消息格式
   - 验证数据库表结构

### 日志文件

- `logs/consumer_main.log`: 主服务日志
- `logs/consumer.log`: 消费者日志
- `logs/database_error.log`: 数据库错误日志

## 性能优化

1. **数据库优化**
   - 调整连接池大小
   - 使用批量插入
   - 添加适当的索引

2. **消息处理优化**
   - 调整QoS设置
   - 使用多线程处理
   - 实现消息批处理

3. **监控优化**
   - 调整指标收集频率
   - 使用采样减少开销

## 许可证

MIT License
