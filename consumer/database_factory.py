"""
数据库工厂类
根据配置创建相应的数据库管理器实例
"""

import yaml
from typing import Dict, Any, Union
import logging

from database_interface import DatabaseInterface
from database_manager import DatabaseManager
from clickhouse_manager import ClickHouseManager


class DatabaseFactory:
    """数据库工厂类"""
    
    @staticmethod
    def create_database_manager(config_path: str = "../config/database.yaml") -> DatabaseInterface:
        """
        根据配置创建数据库管理器
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            DatabaseInterface: 数据库管理器实例
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"数据库配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        
        # 获取数据库类型配置
        db_type = config.get('database_type', 'mysql').lower()
        
        if db_type == 'mysql':
            return DatabaseManager(config_path)
        elif db_type == 'clickhouse':
            return ClickHouseManager(config_path)
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")


class DualDatabaseManager:
    """双数据库管理器 - 同时支持MySQL和ClickHouse"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """
        初始化双数据库管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 获取双写配置
        self.dual_write_config = self.config.get('dual_write', {})
        self.enabled = self.dual_write_config.get('enabled', False)
        self.primary_db = self.dual_write_config.get('primary', 'mysql')
        self.secondary_db = self.dual_write_config.get('secondary', 'clickhouse')
        self.fail_on_secondary_error = self.dual_write_config.get('fail_on_secondary_error', False)
        
        # 创建数据库管理器实例
        self.primary_manager = None
        self.secondary_manager = None
        
        if self.enabled:
            self._init_dual_managers()
        else:
            # 单数据库模式
            self.primary_manager = DatabaseFactory.create_database_manager(config_path)
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"数据库配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _init_dual_managers(self):
        """初始化双数据库管理器"""
        try:
            # 创建主数据库管理器
            if self.primary_db == 'mysql':
                self.primary_manager = DatabaseManager(self.config_path)
            elif self.primary_db == 'clickhouse':
                self.primary_manager = ClickHouseManager(self.config_path)
            else:
                raise ValueError(f"不支持的主数据库类型: {self.primary_db}")
            
            # 创建辅助数据库管理器
            if self.secondary_db == 'mysql':
                self.secondary_manager = DatabaseManager(self.config_path)
            elif self.secondary_db == 'clickhouse':
                self.secondary_manager = ClickHouseManager(self.config_path)
            else:
                raise ValueError(f"不支持的辅助数据库类型: {self.secondary_db}")
            
            self.logger.info(f"双写模式已启用: {self.primary_db} (主) -> {self.secondary_db} (辅)")
            
        except Exception as e:
            self.logger.error(f"初始化双数据库管理器失败: {e}")
            raise
    
    def insert_market_quote(self, symbol: str, quote_data: Dict[str, Any]) -> bool:
        """
        插入市场行情数据
        
        Args:
            symbol: 股票代码
            quote_data: 行情数据
            
        Returns:
            bool: 插入是否成功
        """
        if not self.enabled:
            # 单数据库模式
            return self.primary_manager.insert_market_quote(symbol, quote_data)
        
        # 双写模式
        primary_success = False
        secondary_success = False
        
        # 写入主数据库
        try:
            primary_success = self.primary_manager.insert_market_quote(symbol, quote_data)
        except Exception as e:
            self.logger.error(f"主数据库写入失败 {symbol}: {e}")
        
        # 写入辅助数据库
        if self.secondary_manager:
            try:
                secondary_success = self.secondary_manager.insert_market_quote(symbol, quote_data)
            except Exception as e:
                self.logger.error(f"辅助数据库写入失败 {symbol}: {e}")
                if self.fail_on_secondary_error:
                    return False
        
        # 根据配置决定返回结果
        if self.fail_on_secondary_error:
            return primary_success and secondary_success
        else:
            return primary_success
    
    def log_processing_result(self, message_id: str, symbol: str, status: str,
                            error_message: str = None, raw_data: Dict = None) -> bool:
        """
        记录处理结果
        
        Args:
            message_id: 消息ID
            symbol: 股票代码
            status: 处理状态
            error_message: 错误信息
            raw_data: 原始数据
            
        Returns:
            bool: 记录是否成功
        """
        if not self.enabled:
            return self.primary_manager.log_processing_result(message_id, symbol, status, error_message, raw_data)
        
        # 双写模式
        primary_success = False
        secondary_success = False
        
        try:
            primary_success = self.primary_manager.log_processing_result(message_id, symbol, status, error_message, raw_data)
        except Exception as e:
            self.logger.error(f"主数据库日志记录失败: {e}")
        
        if self.secondary_manager:
            try:
                secondary_success = self.secondary_manager.log_processing_result(message_id, symbol, status, error_message, raw_data)
            except Exception as e:
                self.logger.error(f"辅助数据库日志记录失败: {e}")
        
        return primary_success and (secondary_success if self.fail_on_secondary_error else True)
    
    def get_latest_quotes(self, symbols: list = None) -> list:
        """
        获取最新行情数据（从主数据库读取）
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            list: 行情数据列表
        """
        return self.primary_manager.get_latest_quotes(symbols)
    
    def health_check(self) -> Dict[str, Any]:
        """
        数据库健康检查
        
        Returns:
            Dict: 健康状态信息
        """
        if not self.enabled:
            return self.primary_manager.health_check()
        
        # 双写模式健康检查
        primary_health = self.primary_manager.health_check()
        secondary_health = self.secondary_manager.health_check() if self.secondary_manager else {'status': 'disabled'}
        
        return {
            'dual_write_enabled': True,
            'primary': {
                'type': self.primary_db,
                'health': primary_health
            },
            'secondary': {
                'type': self.secondary_db,
                'health': secondary_health
            },
            'overall_status': 'healthy' if primary_health.get('status') == 'healthy' else 'unhealthy'
        }
    
    def get_database_type(self) -> str:
        """获取数据库类型"""
        if self.enabled:
            return f"dual_{self.primary_db}_{self.secondary_db}"
        else:
            return self.primary_manager.get_database_type()

    def get_pool_size(self) -> int:
        """获取连接池大小"""
        if self.primary_manager:
            return self.primary_manager.get_pool_size()
        return 0
    
    def close(self):
        """关闭数据库连接"""
        if self.primary_manager:
            self.primary_manager.close()
        if self.secondary_manager:
            self.secondary_manager.close()
    
    def batch_insert_market_quotes(self, quotes: list) -> bool:
        """
        批量插入行情数据
        
        Args:
            quotes: 行情数据列表
            
        Returns:
            bool: 批量插入是否成功
        """
        if not self.enabled:
            return self.primary_manager.batch_insert_market_quotes(quotes)
        
        # 双写模式批量插入
        primary_success = False
        secondary_success = False
        
        try:
            primary_success = self.primary_manager.batch_insert_market_quotes(quotes)
        except Exception as e:
            self.logger.error(f"主数据库批量插入失败: {e}")
        
        if self.secondary_manager:
            try:
                secondary_success = self.secondary_manager.batch_insert_market_quotes(quotes)
            except Exception as e:
                self.logger.error(f"辅助数据库批量插入失败: {e}")
        
        if self.fail_on_secondary_error:
            return primary_success and secondary_success
        else:
            return primary_success


# 全局数据库管理器实例
_db_manager = None


def get_database_manager(config_path: str = "config/database.yaml") -> Union[DatabaseInterface, DualDatabaseManager]:
    """获取数据库管理器单例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DualDatabaseManager(config_path)
    return _db_manager


def close_database_manager():
    """关闭数据库管理器"""
    global _db_manager
    if _db_manager:
        _db_manager.close()
        _db_manager = None
