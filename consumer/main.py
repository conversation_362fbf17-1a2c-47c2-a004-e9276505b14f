#!/usr/bin/env python3
"""
单线程市场数据消费者主服务程序
简化版本，去掉多线程，使用单线程处理
支持批量处理和自动重连
"""

import argparse
import logging
import signal
import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from rabbitmq_consumer import get_consumer, close_consumer
from database_factory import get_database_manager, close_database_manager
from metrics import get_metrics_collector, update_system_metrics
from health_check import start_health_server, stop_health_server


class SingleThreadConsumerService:
    """单线程市场数据消费者服务"""
    
    def __init__(self, config_path: str = "config.yaml",
                 db_config_path: str = "config/database.yaml",
                 metrics_port: int = 8001,
                 health_port: int = 8081):
        """
        初始化服务
        
        Args:
            config_path: RabbitMQ配置文件路径
            db_config_path: 数据库配置文件路径
            metrics_port: Prometheus指标端口
            health_port: 健康检查端口
        """
        self.config_path = config_path
        self.db_config_path = db_config_path
        self.metrics_port = metrics_port
        self.health_port = health_port
        
        self.logger = self._setup_logger()
        self.running = False
        
        # 组件实例
        self.consumer = None
        self.db_manager = None
        self.metrics_collector = None
        self.health_server = None
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        # 确保日志目录存在
        log_dir = Path(__file__).parent.parent / 'logs'
        log_dir.mkdir(exist_ok=True)

        # 设置日志文件路径
        log_file = log_dir / 'single_thread_consumer.log'

        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 获取根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)

        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        root_logger.addHandler(console_handler)

        # 文件处理器 - 使用UTF-8编码
        try:
            file_handler = logging.FileHandler(
                log_file,
                mode='a',
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.INFO)
            root_logger.addHandler(file_handler)

            print(f"日志文件已配置: {log_file.absolute()}")

        except Exception as e:
            print(f"配置文件日志处理器失败: {e}")
            print("将只使用控制台输出")

        return logging.getLogger(__name__)
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，开始优雅关闭...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def initialize(self) -> bool:
        """初始化所有组件"""
        try:
            self.logger.info("正在初始化单线程市场数据消费者服务...")
            
            # 初始化Prometheus指标收集器
            self.logger.info(f"启动Prometheus指标服务器，端口: {self.metrics_port}")
            self.metrics_collector = get_metrics_collector(self.metrics_port)
            
            # 初始化数据库管理器
            self.logger.info("初始化数据库管理器...")
            self.db_manager = get_database_manager(self.db_config_path)
            
            # 测试数据库连接
            db_health = self.db_manager.health_check()
            if db_health['status'] != 'healthy':
                self.logger.error(f"数据库连接不健康: {db_health}")
                return False
            
            self.logger.info(f"数据库连接正常 ({self.db_manager.get_database_type()})")
            
            # 初始化RabbitMQ消费者
            self.logger.info("初始化RabbitMQ消费者...")
            self.consumer = get_consumer(self.config_path, self.db_config_path)
            
            if not self.consumer.connect():
                self.logger.error("无法连接到RabbitMQ服务器")
                return False
            
            self.logger.info("RabbitMQ连接正常")
            
            # 启动健康检查服务器
            self.logger.info(f"启动健康检查服务器，端口: {self.health_port}")
            self.health_server = start_health_server('0.0.0.0', self.health_port)
            
            self.logger.info("所有组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            return False
    
    def start(self):
        """启动服务"""
        if not self.initialize():
            self.logger.error("服务初始化失败，退出")
            sys.exit(1)
        
        # 设置信号处理器
        self._setup_signal_handlers()
        
        try:
            self.running = True
            self.logger.info("🚀 单线程市场数据消费者服务已启动")
            self.logger.info(f"📊 健康检查: http://localhost:{self.health_port}/health")
            self.logger.info(f"📈 Prometheus指标: http://localhost:{self.metrics_port}/metrics")
            
            # 显示配置信息
            if hasattr(self.consumer, 'enable_batch_processing'):
                batch_status = "启用" if self.consumer.enable_batch_processing else "禁用"
                self.logger.info(f"🔧 批量处理: {batch_status}")
                if self.consumer.enable_batch_processing:
                    self.logger.info(f"   批量大小: {self.consumer.batch_size}")
                    self.logger.info(f"   批量超时: {self.consumer.batch_timeout}秒")
            
            # 开始消费消息
            self.consumer.start_consuming()
            
        except KeyboardInterrupt:
            self.logger.info("接收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"服务运行时发生错误: {e}")
        finally:
            self.shutdown()
    
    def shutdown(self):
        """优雅关闭服务"""
        if not self.running:
            return
        
        self.logger.info("开始关闭服务...")
        self.running = False
        
        try:
            # 停止消费者
            if self.consumer:
                self.logger.info("停止RabbitMQ消费者...")
                close_consumer()
            
            # 关闭数据库连接
            if self.db_manager:
                self.logger.info("关闭数据库连接...")
                close_database_manager()
            
            # 停止健康检查服务器
            if self.health_server:
                self.logger.info("停止健康检查服务器...")
                stop_health_server()
            
            self.logger.info("✅ 单线程消费者服务已优雅关闭")
            
        except Exception as e:
            self.logger.error(f"关闭服务时发生错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='单线程市场数据消费者服务')
    parser.add_argument('--env', '--environment', default='dev',
                       choices=['dev', 'prod'],
                       help='运行环境 (dev: 开发环境, prod: 生产环境)')
    parser.add_argument('--config', help='RabbitMQ配置文件路径 (可选，会根据环境自动选择)')
    parser.add_argument('--db-config', help='数据库配置文件路径 (可选，会根据环境自动选择)')
    parser.add_argument('--metrics-port', type=int, default=8001, help='Prometheus指标端口')
    parser.add_argument('--health-port', type=int, default=8081, help='健康检查端口')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')

    args = parser.parse_args()

    # 根据环境设置默认配置文件路径
    if not args.config:
        args.config = f'../config/{args.env}/rabbitmq_config.yaml'
    if not args.db_config:
        args.db_config = f'../config/{args.env}/database.yaml'
    
    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # 显示环境信息
    print("=" * 60)
    print("🔧 单线程市场数据消费者")
    print("=" * 60)
    print(f"🌍 运行环境: {args.env.upper()}")
    print(f"📁 RabbitMQ配置: {args.config}")
    print(f"📁 数据库配置: {args.db_config}")
    print(f"📊 指标端口: {args.metrics_port}")
    print(f"🏥 健康检查端口: {args.health_port}")
    print(f"📝 日志级别: {args.log_level}")
    print("=" * 60)

    # 创建并启动服务
    service = SingleThreadConsumerService(
        config_path=args.config,
        db_config_path=args.db_config,
        metrics_port=args.metrics_port,
        health_port=args.health_port
    )

    service.start()


if __name__ == '__main__':
    main()
