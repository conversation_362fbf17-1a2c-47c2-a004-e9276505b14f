#!/usr/bin/env python3
"""
简单的配置文件验证脚本
不依赖外部库，只检查文件内容
"""

import os


def verify_rabbitmq_config():
    """验证RabbitMQ配置文件内容"""
    print("🔍 验证RabbitMQ配置文件...")
    
    config_path = "config/rabbitmq_config.yaml"
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置项
        required_items = [
            'rabbitmq:',
            'host:',
            'port:',
            'username:',
            'password:',
            'queue:',
            'exchange:',
            'routing_key:',
            'market_data_queue',
            'market_data_exchange',
            'market.data'
        ]
        
        missing_items = []
        for item in required_items:
            if item not in content:
                missing_items.append(item)
        
        if missing_items:
            print(f"❌ 配置文件缺少以下项目: {missing_items}")
            return False
        
        print("✅ RabbitMQ配置文件内容验证通过")
        print(f"   文件大小: {len(content)} 字符")
        
        # 显示配置文件的前几行
        lines = content.split('\n')[:10]
        print("   配置文件开头:")
        for i, line in enumerate(lines, 1):
            if line.strip():
                print(f"     {i:2d}: {line}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False


def verify_database_config():
    """验证数据库配置文件内容"""
    print("\n🔍 验证数据库配置文件...")
    
    config_path = "config/database.yaml"
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置项
        required_items = [
            'database:',
            'primary:',
            'host:',
            'port:',
            'username:',
            'password:',
            'database:',
            'pool:',
            'market_data'
        ]
        
        missing_items = []
        for item in required_items:
            if item not in content:
                missing_items.append(item)
        
        if missing_items:
            print(f"❌ 配置文件缺少以下项目: {missing_items}")
            return False
        
        print("✅ 数据库配置文件内容验证通过")
        print(f"   文件大小: {len(content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False


def verify_file_paths():
    """验证producer中的文件路径引用"""
    print("\n🔍 验证producer文件路径引用...")
    
    collect_data_path = "producer/collect_data.py"
    rabbitmq_client_path = "producer/rabbitmq_client.py"
    
    files_to_check = [
        (collect_data_path, "../config/rabbitmq_config.yaml"),
        (rabbitmq_client_path, "../config/rabbitmq_config.yaml")
    ]
    
    for file_path, expected_config_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if expected_config_path in content:
                print(f"✅ {file_path} 包含正确的配置路径")
            else:
                print(f"❌ {file_path} 未找到配置路径: {expected_config_path}")
                return False
                
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path}: {e}")
            return False
    
    return True


def verify_project_structure():
    """验证项目结构"""
    print("\n🔍 验证项目结构...")
    
    expected_structure = {
        'config/': [
            'rabbitmq_config.yaml',
            'database.yaml',
            'database_schema.sql'
        ],
        'producer/': [
            'collect_data.py',
            'rabbitmq_client.py',
            'README.md'
        ],
        'consumer/': [
            'main.py',
            'database_manager.py',
            'rabbitmq_consumer.py',
            'README.md'
        ],
        'monitoring/': [
            'prometheus.yml'
        ],
        './': [
            'docker-compose.yml',
            'requirements.txt',
            'README.md',
            'producer_metrics.py',
            'producer_health.py'
        ]
    }
    
    all_good = True
    
    for directory, files in expected_structure.items():
        print(f"\n   检查目录: {directory}")
        
        for file_name in files:
            file_path = os.path.join(directory, file_name) if directory != './' else file_name
            
            if os.path.exists(file_path):
                print(f"     ✅ {file_name}")
            else:
                print(f"     ❌ {file_name} (缺失)")
                all_good = False
    
    return all_good


def main():
    """主验证函数"""
    print("🚀 开始配置文件和项目验证")
    print("=" * 50)
    
    tests = [
        ("项目结构", verify_project_structure),
        ("RabbitMQ配置文件", verify_rabbitmq_config),
        ("数据库配置文件", verify_database_config),
        ("文件路径引用", verify_file_paths)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 验证: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 验证通过")
            else:
                print(f"❌ {test_name} 验证失败")
        except Exception as e:
            print(f"❌ {test_name} 验证异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有验证通过！配置文件和项目结构正确")
        print("\n📋 下一步:")
        print("   1. 安装依赖: pip install -r requirements.txt")
        print("   2. 启动服务: cd producer && python collect_data.py")
        print("   3. 测试监控: python test_producer_system.py")
        return 0
    else:
        print("⚠️  部分验证失败，请检查配置文件和项目结构")
        return 1


if __name__ == '__main__':
    exit(main())
