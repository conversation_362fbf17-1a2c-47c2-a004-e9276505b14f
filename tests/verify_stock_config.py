#!/usr/bin/env python3
"""
简单的股票配置验证脚本
验证配置文件读取功能是否正常
"""

import os


def verify_config_files():
    """验证配置文件存在"""
    print("🔍 验证配置文件...")
    
    config_files = [
        "config/stocks_config.yaml",
        "config/rabbitmq_config.yaml",
        "config/database.yaml"
    ]
    
    all_exist = True
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file}")
        else:
            print(f"❌ {config_file} (缺失)")
            all_exist = False
    
    return all_exist


def verify_stock_config_content():
    """验证股票配置文件内容"""
    print("\n🔍 验证股票配置内容...")
    
    config_path = "config/stocks_config.yaml"
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置项
        required_items = [
            'stocks:',
            'default_list:',
            '600030.SH',  # 示例股票代码
            'subscription:',
            'active_group:',
            'period:',
            'delay_between_stocks:'
        ]
        
        missing_items = []
        for item in required_items:
            if item not in content:
                missing_items.append(item)
        
        if missing_items:
            print(f"❌ 缺少配置项: {missing_items}")
            return False
        
        # 统计股票代码
        sh_stocks = content.count('.SH')
        sz_stocks = content.count('.SZ')
        total_stocks = sh_stocks + sz_stocks
        
        print(f"✅ 股票配置验证通过")
        print(f"   上海股票: {sh_stocks} 只")
        print(f"   深圳股票: {sz_stocks} 只")
        print(f"   总计: {total_stocks} 只")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def verify_stock_loader():
    """验证股票加载器"""
    print("\n🔍 验证股票加载器...")
    
    try:
        from producer.stock_config_loader import load_stock_list, get_subscription_config
        
        # 测试加载默认股票列表
        stocks = load_stock_list(config_path="config/stocks_config.yaml")
        print(f"✅ 成功加载 {len(stocks)} 只股票")
        print(f"   示例股票: {stocks[:3]}")
        
        # 测试加载订阅配置
        sub_config = get_subscription_config(config_path="config/stocks_config.yaml")
        print(f"✅ 订阅配置: period={sub_config.get('period')}, count={sub_config.get('count')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def verify_producer_modification():
    """验证producer修改"""
    print("\n🔍 验证producer修改...")
    
    try:
        with open('producer/main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        required_changes = [
            'from stock_config_loader import',
            'load_stocks_from_config',
            'load_stock_list(',
            'subscription_config',
            'period = subscription_config.get'
        ]
        
        missing_changes = []
        for change in required_changes:
            if change in content:
                print(f"     ✅ {change}")
            else:
                missing_changes.append(change)
                print(f"     ❌ {change}")
        
        if missing_changes:
            print(f"❌ 缺少修改: {missing_changes}")
            return False
        
        print("✅ Producer修改验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主验证函数"""
    print("🚀 开始股票配置功能验证")
    print("=" * 50)
    
    tests = [
        ("配置文件存在性", verify_config_files),
        ("股票配置内容", verify_stock_config_content),
        ("股票加载器", verify_stock_loader),
        ("Producer修改", verify_producer_modification)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 验证: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 验证通过")
            else:
                print(f"❌ {test_name} 验证失败")
        except Exception as e:
            print(f"❌ {test_name} 验证异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 股票配置功能验证全部通过！")
        print("\n📋 配置文件读取功能已就绪:")
        print("   ✅ config/stocks_config.yaml - 股票列表配置")
        print("   ✅ stock_config_loader.py - 配置加载器")
        print("   ✅ producer/main.py - 集成配置读取")
        print("\n🔧 支持的功能:")
        print("   • 多股票组管理 (default_list, banking, technology, all)")
        print("   • 可配置订阅参数 (period, count, delay)")
        print("   • 过滤器支持")
        print("   • 动态配置重载")
        print("\n🚀 启动服务:")
        print("   cd producer && python main.py")
        return 0
    else:
        print("⚠️  部分验证失败，请检查配置和代码")
        return 1


if __name__ == '__main__':
    exit(main())
