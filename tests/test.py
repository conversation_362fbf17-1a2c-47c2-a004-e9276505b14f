from xtquant import xtdata


def callback_func(data):
    print(data)

#xtdata.subscribe_quote('600030.SH', period='1m', count=-1, callback=callback_func)
#xtdata.subscribe_whole_quote(['600030.SH'],callback_func)
#xtdata.subscribe_quote2(stock_code='600030.SH', period='1m', count=-1, callback=callback_func)


# 十当行情
xtdata.subscribe_quote2(stock_code='600030.SH',period='tick',callback=callback_func)

print("已订阅")

# 死循环 阻塞主线程退出
xtdata.run()