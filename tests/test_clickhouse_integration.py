#!/usr/bin/env python3
"""
ClickHouse集成测试
测试ClickHouse数据库管理器的功能
"""

import sys
import os
import unittest
import tempfile
import yaml
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from consumer.clickhouse_manager import ClickHouseManager
    from consumer.database_factory import DatabaseFactory, DualDatabaseManager
    CLICKHOUSE_AVAILABLE = True
except ImportError as e:
    print(f"ClickHouse依赖不可用: {e}")
    CLICKHOUSE_AVAILABLE = False


class TestClickHouseManager(unittest.TestCase):
    """ClickHouse管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        if not CLICKHOUSE_AVAILABLE:
            self.skipTest("ClickHouse依赖不可用")
        
        # 创建临时配置文件
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        config_data = {
            'clickhouse': {
                'primary': {
                    'host': '127.0.0.1',
                    'port': 9000,
                    'username': 'default',
                    'password': '',
                    'database': 'test_market_data',
                    'connect_timeout': 10,
                    'send_receive_timeout': 300,
                    'compression': True
                },
                'pool': {
                    'min_connections': 1,
                    'max_connections': 5
                },
                'batch': {
                    'size': 1000
                }
            }
        }
        yaml.dump(config_data, self.temp_config)
        self.temp_config.close()
        
        # 模拟ClickHouse客户端
        self.mock_client = Mock()
        self.mock_client.execute.return_value = [[1]]
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'temp_config'):
            os.unlink(self.temp_config.name)
    
    @patch('consumer.clickhouse_manager.Client')
    def test_clickhouse_manager_init(self, mock_client_class):
        """测试ClickHouse管理器初始化"""
        mock_client_class.return_value = self.mock_client
        
        manager = ClickHouseManager(self.temp_config.name)
        
        self.assertEqual(manager.get_database_type(), 'clickhouse')
        self.assertIsNotNone(manager.config)
        self.assertIsNotNone(manager.logger)
    
    @patch('consumer.clickhouse_manager.Client')
    def test_insert_market_quote(self, mock_client_class):
        """测试插入行情数据"""
        mock_client_class.return_value = self.mock_client
        
        manager = ClickHouseManager(self.temp_config.name)
        
        # 测试数据
        symbol = "600000.SH"
        quote_data = {
            'time': 1719115200000,  # 毫秒时间戳
            'timetag': '20250623 10:00:00',
            'lastPrice': 10.5,
            'open': 10.0,
            'high': 10.8,
            'low': 9.8,
            'lastClose': 10.2,
            'amount': 1000000,
            'volume': 50000,
            'pvolume': 500000,
            'stockStatus': 1,
            'openInt': 0,
            'settlementPrice': None,
            'lastSettlementPrice': None,
            'askPrice': [10.6, 10.7, 10.8, 10.9, 11.0],
            'bidPrice': [10.5, 10.4, 10.3, 10.2, 10.1],
            'askVol': [100, 200, 300, 400, 500],
            'bidVol': [150, 250, 350, 450, 550]
        }
        
        # 执行插入
        result = manager.insert_market_quote(symbol, quote_data)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证调用了execute方法
        self.assertTrue(mock_client_class.called)
        self.assertTrue(self.mock_client.execute.called)
        
        # 验证插入了两次（行情数据和盘口数据）
        self.assertEqual(self.mock_client.execute.call_count, 2)
    
    @patch('consumer.clickhouse_manager.Client')
    def test_insert_market_quote_without_depth(self, mock_client_class):
        """测试插入没有盘口数据的行情"""
        mock_client_class.return_value = self.mock_client
        
        manager = ClickHouseManager(self.temp_config.name)
        
        # 测试数据（无盘口数据）
        symbol = "600000.SH"
        quote_data = {
            'time': 1719115200000,
            'timetag': '20250623 10:00:00',
            'lastPrice': 10.5,
            'open': 10.0,
            'high': 10.8,
            'low': 9.8,
            'volume': 50000
        }
        
        # 执行插入
        result = manager.insert_market_quote(symbol, quote_data)
        
        # 验证结果
        self.assertTrue(result)
        
        # 验证只插入了一次（仅行情数据）
        self.assertEqual(self.mock_client.execute.call_count, 1)
    
    @patch('consumer.clickhouse_manager.Client')
    def test_log_processing_result(self, mock_client_class):
        """测试记录处理结果"""
        mock_client_class.return_value = self.mock_client
        
        manager = ClickHouseManager(self.temp_config.name)
        
        # 测试数据
        message_id = "test-message-123"
        symbol = "600000.SH"
        status = "SUCCESS"
        error_message = None
        raw_data = {"test": "data"}
        
        # 执行记录
        result = manager.log_processing_result(message_id, symbol, status, error_message, raw_data)
        
        # 验证结果
        self.assertTrue(result)
        self.assertTrue(self.mock_client.execute.called)
    
    @patch('consumer.clickhouse_manager.Client')
    def test_get_latest_quotes(self, mock_client_class):
        """测试获取最新行情"""
        # 模拟查询结果
        mock_result = [
            ('600000.SH', datetime.now(), 1719115200000, '20250623 10:00:00', 
             10.5, 10.0, 10.8, 9.8, 10.2, 1000000, 50000, 500000, 1, 0, None, None, datetime.now(), 1)
        ]
        self.mock_client.execute.return_value = mock_result
        mock_client_class.return_value = self.mock_client
        
        manager = ClickHouseManager(self.temp_config.name)
        
        # 测试获取指定股票的最新行情
        symbols = ["600000.SH"]
        result = manager.get_latest_quotes(symbols)
        
        # 验证结果
        self.assertIsInstance(result, list)
        self.assertEqual(len(result), 1)
        self.assertIsInstance(result[0], dict)
        self.assertEqual(result[0]['symbol'], '600000.SH')
    
    @patch('consumer.clickhouse_manager.Client')
    def test_health_check(self, mock_client_class):
        """测试健康检查"""
        # 模拟健康检查结果
        self.mock_client.execute.side_effect = [
            [[1]],  # SELECT 1
            [('market_quotes', 1000, 1024000), ('market_depth', 500, 512000)],  # 表信息
            [[datetime.now()]]  # 最新数据时间
        ]
        mock_client_class.return_value = self.mock_client
        
        manager = ClickHouseManager(self.temp_config.name)
        
        # 执行健康检查
        result = manager.health_check()
        
        # 验证结果
        self.assertIsInstance(result, dict)
        self.assertEqual(result['status'], 'healthy')
        self.assertEqual(result['database_type'], 'clickhouse')
        self.assertIn('tables_info', result)
    
    @patch('consumer.clickhouse_manager.Client')
    def test_batch_insert(self, mock_client_class):
        """测试批量插入"""
        mock_client_class.return_value = self.mock_client
        
        manager = ClickHouseManager(self.temp_config.name)
        
        # 准备批量数据
        quotes = []
        for i in range(5):
            quotes.append({
                'symbol': f'60000{i}.SH',
                'quote_data': {
                    'time': 1719115200000 + i * 1000,
                    'timetag': f'20250623 10:00:0{i}',
                    'lastPrice': 10.0 + i * 0.1,
                    'volume': 1000 * (i + 1)
                }
            })
        
        # 执行批量插入
        result = manager.batch_insert_market_quotes(quotes)
        
        # 验证结果
        self.assertTrue(result)
        self.assertTrue(self.mock_client.execute.called)


class TestDatabaseFactory(unittest.TestCase):
    """数据库工厂测试"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时配置文件
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'temp_config'):
            os.unlink(self.temp_config.name)
    
    def test_create_mysql_manager(self):
        """测试创建MySQL管理器"""
        config_data = {
            'database_type': 'mysql',
            'database': {
                'primary': {
                    'host': '127.0.0.1',
                    'port': 3306,
                    'username': 'root',
                    'password': 'root',
                    'database': 'test_db',
                    'charset': 'utf8mb4'
                },
                'pool': {'min_connections': 1, 'max_connections': 5},
                'connection': {
                    'connect_timeout': 10,
                    'read_timeout': 30,
                    'write_timeout': 30,
                    'autocommit': True
                }
            }
        }
        yaml.dump(config_data, self.temp_config)
        self.temp_config.close()
        
        with patch('consumer.database_manager.DatabaseManager') as mock_manager:
            manager = DatabaseFactory.create_database_manager(self.temp_config.name)
            mock_manager.assert_called_once_with(self.temp_config.name)
    
    @unittest.skipUnless(CLICKHOUSE_AVAILABLE, "ClickHouse依赖不可用")
    def test_create_clickhouse_manager(self):
        """测试创建ClickHouse管理器"""
        config_data = {
            'database_type': 'clickhouse',
            'clickhouse': {
                'primary': {
                    'host': '127.0.0.1',
                    'port': 9000,
                    'username': 'default',
                    'password': '',
                    'database': 'test_db'
                },
                'pool': {'min_connections': 1, 'max_connections': 5}
            }
        }
        yaml.dump(config_data, self.temp_config)
        self.temp_config.close()
        
        with patch('consumer.clickhouse_manager.ClickHouseManager') as mock_manager:
            manager = DatabaseFactory.create_database_manager(self.temp_config.name)
            mock_manager.assert_called_once_with(self.temp_config.name)
    
    def test_unsupported_database_type(self):
        """测试不支持的数据库类型"""
        config_data = {
            'database_type': 'postgresql'
        }
        yaml.dump(config_data, self.temp_config)
        self.temp_config.close()
        
        with self.assertRaises(ValueError):
            DatabaseFactory.create_database_manager(self.temp_config.name)


class TestDualDatabaseManager(unittest.TestCase):
    """双数据库管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        if not CLICKHOUSE_AVAILABLE:
            self.skipTest("ClickHouse依赖不可用")
        
        # 创建临时配置文件
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'temp_config'):
            os.unlink(self.temp_config.name)
    
    def test_single_database_mode(self):
        """测试单数据库模式"""
        config_data = {
            'database_type': 'mysql',
            'dual_write': {'enabled': False},
            'database': {
                'primary': {
                    'host': '127.0.0.1',
                    'port': 3306,
                    'username': 'root',
                    'password': 'root',
                    'database': 'test_db',
                    'charset': 'utf8mb4'
                },
                'pool': {'min_connections': 1, 'max_connections': 5},
                'connection': {
                    'connect_timeout': 10,
                    'read_timeout': 30,
                    'write_timeout': 30,
                    'autocommit': True
                }
            }
        }
        yaml.dump(config_data, self.temp_config)
        self.temp_config.close()
        
        with patch('consumer.database_manager.DatabaseManager') as mock_manager:
            dual_manager = DualDatabaseManager(self.temp_config.name)
            
            self.assertFalse(dual_manager.enabled)
            self.assertIsNotNone(dual_manager.primary_manager)
            self.assertIsNone(dual_manager.secondary_manager)
    
    def test_dual_database_mode(self):
        """测试双数据库模式"""
        config_data = {
            'database_type': 'mysql',
            'dual_write': {
                'enabled': True,
                'primary': 'mysql',
                'secondary': 'clickhouse',
                'fail_on_secondary_error': False
            },
            'database': {
                'primary': {
                    'host': '127.0.0.1',
                    'port': 3306,
                    'username': 'root',
                    'password': 'root',
                    'database': 'test_db',
                    'charset': 'utf8mb4'
                },
                'pool': {'min_connections': 1, 'max_connections': 5},
                'connection': {
                    'connect_timeout': 10,
                    'read_timeout': 30,
                    'write_timeout': 30,
                    'autocommit': True
                }
            },
            'clickhouse': {
                'primary': {
                    'host': '127.0.0.1',
                    'port': 9000,
                    'username': 'default',
                    'password': '',
                    'database': 'test_db'
                },
                'pool': {'min_connections': 1, 'max_connections': 5}
            }
        }
        yaml.dump(config_data, self.temp_config)
        self.temp_config.close()
        
        with patch('consumer.database_manager.DatabaseManager') as mock_mysql, \
             patch('consumer.clickhouse_manager.ClickHouseManager') as mock_clickhouse:
            
            dual_manager = DualDatabaseManager(self.temp_config.name)
            
            self.assertTrue(dual_manager.enabled)
            self.assertIsNotNone(dual_manager.primary_manager)
            self.assertIsNotNone(dual_manager.secondary_manager)
            self.assertEqual(dual_manager.primary_db, 'mysql')
            self.assertEqual(dual_manager.secondary_db, 'clickhouse')


class TestClickHouseIntegration(unittest.TestCase):
    """ClickHouse集成测试（需要真实的ClickHouse服务器）"""

    @classmethod
    def setUpClass(cls):
        """类级别的设置"""
        if not CLICKHOUSE_AVAILABLE:
            raise unittest.SkipTest("ClickHouse依赖不可用")

        # 尝试连接到ClickHouse服务器
        try:
            from clickhouse_driver import Client
            client = Client(host='127.0.0.1', port=9000, user='default', password='')
            client.execute("SELECT 1")
            cls.clickhouse_available = True
        except Exception as e:
            raise unittest.SkipTest(f"ClickHouse服务器不可用: {e}")

    def setUp(self):
        """测试前准备"""
        # 创建测试配置
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        config_data = {
            'database_type': 'clickhouse',
            'clickhouse': {
                'primary': {
                    'host': '127.0.0.1',
                    'port': 9000,
                    'username': 'default',
                    'password': '',
                    'database': 'test_market_data',
                    'connect_timeout': 10,
                    'send_receive_timeout': 300,
                    'compression': True
                },
                'pool': {
                    'min_connections': 1,
                    'max_connections': 3
                },
                'batch': {
                    'size': 100
                }
            }
        }
        yaml.dump(config_data, self.temp_config)
        self.temp_config.close()

        # 创建测试数据库
        from clickhouse_driver import Client
        client = Client(host='127.0.0.1', port=9000, user='default', password='')
        try:
            client.execute("CREATE DATABASE IF NOT EXISTS test_market_data")
            client.execute("USE test_market_data")

            # 创建测试表
            client.execute("""
                CREATE TABLE IF NOT EXISTS market_quotes (
                    symbol String,
                    time_dt DateTime,
                    time UInt64,
                    timetag String,
                    last_price Nullable(Decimal64(3)),
                    open_price Nullable(Decimal64(3)),
                    high_price Nullable(Decimal64(3)),
                    low_price Nullable(Decimal64(3)),
                    last_close Nullable(Decimal64(3)),
                    amount Nullable(UInt64),
                    volume Nullable(UInt64),
                    pvolume Nullable(UInt64),
                    stock_status Nullable(Int32),
                    open_int Nullable(Int32),
                    settlement_price Nullable(Decimal64(3)),
                    last_settlement_price Nullable(Decimal64(3)),
                    created_at DateTime DEFAULT now()
                ) ENGINE = MergeTree()
                ORDER BY (symbol, time_dt)
            """)

            client.execute("""
                CREATE TABLE IF NOT EXISTS market_depth (
                    symbol String,
                    time_dt DateTime,
                    time UInt64,
                    timetag String,
                    ask_price_1 Nullable(Decimal64(3)),
                    ask_price_2 Nullable(Decimal64(3)),
                    ask_price_3 Nullable(Decimal64(3)),
                    ask_price_4 Nullable(Decimal64(3)),
                    ask_price_5 Nullable(Decimal64(3)),
                    bid_price_1 Nullable(Decimal64(3)),
                    bid_price_2 Nullable(Decimal64(3)),
                    bid_price_3 Nullable(Decimal64(3)),
                    bid_price_4 Nullable(Decimal64(3)),
                    bid_price_5 Nullable(Decimal64(3)),
                    ask_vol_1 Nullable(UInt64),
                    ask_vol_2 Nullable(UInt64),
                    ask_vol_3 Nullable(UInt64),
                    ask_vol_4 Nullable(UInt64),
                    ask_vol_5 Nullable(UInt64),
                    bid_vol_1 Nullable(UInt64),
                    bid_vol_2 Nullable(UInt64),
                    bid_vol_3 Nullable(UInt64),
                    bid_vol_4 Nullable(UInt64),
                    bid_vol_5 Nullable(UInt64),
                    created_at DateTime DEFAULT now()
                ) ENGINE = MergeTree()
                ORDER BY (symbol, time_dt)
            """)

            client.execute("""
                CREATE TABLE IF NOT EXISTS processing_log (
                    message_id String,
                    symbol String,
                    processing_time DateTime,
                    status Enum8('SUCCESS' = 1, 'FAILED' = 2, 'RETRY' = 3),
                    error_message Nullable(String),
                    raw_data Nullable(String)
                ) ENGINE = MergeTree()
                ORDER BY (processing_time, symbol)
            """)

        except Exception as e:
            self.skipTest(f"无法创建测试表: {e}")
        finally:
            client.disconnect()

    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'temp_config'):
            os.unlink(self.temp_config.name)

        # 清理测试数据
        try:
            from clickhouse_driver import Client
            client = Client(host='127.0.0.1', port=9000, user='default', password='')
            client.execute("DROP DATABASE IF EXISTS test_market_data")
            client.disconnect()
        except:
            pass

    def test_real_clickhouse_insert_and_query(self):
        """测试真实的ClickHouse插入和查询"""
        manager = ClickHouseManager(self.temp_config.name)

        # 测试数据
        symbol = "600000.SH"
        quote_data = {
            'time': 1719115200000,
            'timetag': '20250623 10:00:00',
            'lastPrice': 10.5,
            'open': 10.0,
            'high': 10.8,
            'low': 9.8,
            'lastClose': 10.2,
            'amount': 1000000,
            'volume': 50000,
            'pvolume': 500000,
            'stockStatus': 1,
            'openInt': 0,
            'askPrice': [10.6, 10.7, 10.8, 10.9, 11.0],
            'bidPrice': [10.5, 10.4, 10.3, 10.2, 10.1],
            'askVol': [100, 200, 300, 400, 500],
            'bidVol': [150, 250, 350, 450, 550]
        }

        # 测试插入
        result = manager.insert_market_quote(symbol, quote_data)
        self.assertTrue(result, "插入行情数据失败")

        # 测试查询
        quotes = manager.get_latest_quotes([symbol])
        self.assertGreater(len(quotes), 0, "查询不到插入的数据")

        # 验证数据
        quote = quotes[0]
        self.assertEqual(quote['symbol'], symbol)
        self.assertEqual(float(quote['last_price']), 10.5)

        # 测试健康检查
        health = manager.health_check()
        self.assertEqual(health['status'], 'healthy')
        self.assertEqual(health['database_type'], 'clickhouse')

        # 测试日志记录
        log_result = manager.log_processing_result(
            message_id="test-123",
            symbol=symbol,
            status="SUCCESS",
            error_message=None,
            raw_data=quote_data
        )
        self.assertTrue(log_result, "记录处理日志失败")


def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()

    # 添加测试类
    test_classes = [
        TestClickHouseManager,
        TestDatabaseFactory,
        TestDualDatabaseManager,
        TestClickHouseIntegration  # 集成测试
    ]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    return result.wasSuccessful()


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
