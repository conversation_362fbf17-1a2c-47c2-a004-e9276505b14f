#!/usr/bin/env python3
"""
测试日志编码功能
验证中文日志是否正确输出
"""

import sys
import os
import logging
import time

# 添加项目根目录到Python路径
sys.path.append('..')

def test_logging_setup():
    """测试日志设置"""
    print("🧪 测试日志设置...")
    
    try:
        # 导入main模块来使用其日志设置
        import main
        
        # 获取logger
        logger = logging.getLogger('test_logger')
        
        print("✅ 日志设置导入成功")
        
        # 测试各种中文日志
        test_messages = [
            "测试中文日志输出",
            "股票代码: 600000.SH 浦发银行",
            "订阅成功: 中信证券 (600030.SH)",
            "错误信息: 连接失败，请检查网络",
            "监控指标: CPU使用率 45.2%，内存使用率 67.8%",
            "数据处理: 接收到 1000 条行情数据",
            "系统状态: 服务运行正常，已处理 50000 条消息"
        ]
        
        print("\n📝 写入测试日志...")
        for i, message in enumerate(test_messages, 1):
            logger.info(f"[测试{i:02d}] {message}")
            time.sleep(0.1)  # 短暂延迟
        
        logger.warning("⚠️ 这是一条警告信息")
        logger.error("❌ 这是一条错误信息")
        
        print("✅ 测试日志写入完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def check_log_file():
    """检查日志文件内容"""
    print("\n🔍 检查日志文件...")
    
    log_file = '../logs/producer.log'
    
    if not os.path.exists(log_file):
        print(f"❌ 日志文件不存在: {log_file}")
        return False
    
    try:
        # 使用UTF-8编码读取日志文件
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✅ 日志文件读取成功 (UTF-8编码)")
        print(f"   文件大小: {len(content)} 字符")
        
        # 检查是否包含中文
        chinese_chars = sum(1 for char in content if '\u4e00' <= char <= '\u9fff')
        print(f"   中文字符数: {chinese_chars}")
        
        # 显示最后几行日志
        lines = content.strip().split('\n')
        if lines:
            print(f"   总行数: {len(lines)}")
            print("   最后5行日志:")
            for line in lines[-5:]:
                if line.strip():
                    print(f"     {line}")
        
        return True
        
    except UnicodeDecodeError as e:
        print(f"❌ UTF-8解码失败: {e}")
        
        # 尝试其他编码
        encodings = ['gbk', 'gb2312', 'latin1']
        for encoding in encodings:
            try:
                with open(log_file, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"⚠️  使用 {encoding} 编码可以读取文件")
                break
            except:
                continue
        else:
            print("❌ 无法使用常见编码读取文件")
        
        return False
        
    except Exception as e:
        print(f"❌ 读取日志文件失败: {e}")
        return False


def test_log_rotation():
    """测试日志轮转"""
    print("\n🔄 测试日志轮转...")
    
    log_file = '../logs/producer.log'
    
    try:
        # 获取当前文件大小
        if os.path.exists(log_file):
            original_size = os.path.getsize(log_file)
            print(f"   原始文件大小: {original_size} 字节")
        else:
            original_size = 0
            print("   日志文件不存在，将创建新文件")
        
        # 写入大量日志
        logger = logging.getLogger('rotation_test')
        
        print("   写入大量测试数据...")
        for i in range(100):
            logger.info(f"日志轮转测试 #{i:03d}: 这是一条包含中文的测试日志，用于验证编码和轮转功能")
        
        # 检查文件大小变化
        if os.path.exists(log_file):
            new_size = os.path.getsize(log_file)
            print(f"   新文件大小: {new_size} 字节")
            print(f"   增加了: {new_size - original_size} 字节")
        
        print("✅ 日志轮转测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 日志轮转测试失败: {e}")
        return False


def verify_encoding():
    """验证编码设置"""
    print("\n🔧 验证编码设置...")
    
    try:
        # 检查系统编码
        print(f"   系统默认编码: {sys.getdefaultencoding()}")
        print(f"   文件系统编码: {sys.getfilesystemencoding()}")
        
        # 检查环境变量
        lang = os.environ.get('LANG', 'Not set')
        pythonioencoding = os.environ.get('PYTHONIOENCODING', 'Not set')
        print(f"   LANG环境变量: {lang}")
        print(f"   PYTHONIOENCODING: {pythonioencoding}")
        
        # 测试中文字符串
        test_str = "测试中文字符串: 股票代码 600000.SH"
        encoded = test_str.encode('utf-8')
        decoded = encoded.decode('utf-8')
        
        if test_str == decoded:
            print("✅ UTF-8编码测试通过")
        else:
            print("❌ UTF-8编码测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 编码验证失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始日志编码测试")
    print("=" * 50)
    
    tests = [
        ("编码设置验证", verify_encoding),
        ("日志设置测试", test_logging_setup),
        ("日志文件检查", check_log_file),
        ("日志轮转测试", test_log_rotation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 日志编码测试全部通过！")
        print("\n📋 修复内容:")
        print("   ✅ 设置UTF-8编码的FileHandler")
        print("   ✅ 清理重复的日志处理器")
        print("   ✅ 添加日志目录自动创建")
        print("   ✅ 优化日志格式和时间格式")
        print("\n📝 日志文件位置:")
        log_path = os.path.abspath('../logs/producer.log')
        print(f"   {log_path}")
        return 0
    else:
        print("⚠️  部分测试失败，请检查日志配置")
        return 1


if __name__ == '__main__':
    exit(main())
