#!/usr/bin/env python3
"""
股票配置文件测试脚本
验证股票配置加载器功能
"""

import sys
import os

def test_stock_config_loader():
    """测试股票配置加载器"""
    print("🧪 测试股票配置加载器...")
    
    try:
        from producer.stock_config_loader import StockConfigLoader
        
        # 创建加载器实例
        loader = StockConfigLoader("config/stocks_config.yaml")
        
        print("✅ 股票配置加载器创建成功")
        
        # 获取配置摘要
        summary = loader.get_config_summary()
        print("\n📋 配置摘要:")
        for key, value in summary.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_load_stock_list():
    """测试加载股票列表"""
    print("\n🧪 测试加载股票列表...")
    
    try:
        from producer.stock_config_loader import load_stock_list, get_subscription_config
        
        # 测试默认组
        default_stocks = load_stock_list(config_path="config/stocks_config.yaml")
        print(f"✅ 默认股票组: {len(default_stocks)} 只股票")
        print(f"   前5只: {default_stocks[:5]}")
        
        # 测试其他组
        try:
            banking_stocks = load_stock_list("banking", "config/stocks_config.yaml")
            print(f"✅ 银行股票组: {len(banking_stocks)} 只股票")
            print(f"   银行股票: {banking_stocks}")
        except Exception as e:
            print(f"⚠️  银行股票组加载失败: {e}")
        
        # 测试所有股票
        try:
            all_stocks = load_stock_list("all", "config/stocks_config.yaml")
            print(f"✅ 所有股票: {len(all_stocks)} 只股票")
        except Exception as e:
            print(f"⚠️  所有股票加载失败: {e}")
        
        # 测试订阅配置
        sub_config = get_subscription_config("config/stocks_config.yaml")
        print(f"✅ 订阅配置: {sub_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_producer_integration():
    """测试producer集成"""
    print("\n🧪 测试producer集成...")
    
    try:
        # 添加producer目录到路径
        sys.path.append('producer')
        
        # 导入main模块
        import main
        
        # 检查是否有load_stocks_from_config函数
        if hasattr(main, 'load_stocks_from_config'):
            print("✅ 找到load_stocks_from_config函数")
            
            # 测试加载函数
            try:
                code, sub_config = main.load_stocks_from_config()
                print(f"✅ 成功加载股票配置")
                print(f"   股票数量: {len(code)}")
                print(f"   前5只股票: {code[:5]}")
                print(f"   订阅配置: {sub_config}")
                
                return True
                
            except Exception as e:
                print(f"❌ 加载股票配置失败: {e}")
                return False
        else:
            print("❌ 未找到load_stocks_from_config函数")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_config_file_content():
    """测试配置文件内容"""
    print("\n🧪 测试配置文件内容...")
    
    config_path = "config/stocks_config.yaml"
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键配置项
        required_items = [
            'stocks:',
            'default_list:',
            'subscription:',
            'active_group:',
            'period:',
            'count:',
            'delay_between_stocks:'
        ]
        
        missing_items = []
        for item in required_items:
            if item not in content:
                missing_items.append(item)
        
        if missing_items:
            print(f"❌ 配置文件缺少以下项目: {missing_items}")
            return False
        
        print("✅ 配置文件内容验证通过")
        print(f"   文件大小: {len(content)} 字符")
        
        # 统计股票数量
        lines = content.split('\n')
        stock_lines = [line for line in lines if '.SH' in line or '.SZ' in line]
        print(f"   包含股票代码的行数: {len(stock_lines)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False


def test_yaml_parsing():
    """测试YAML解析"""
    print("\n🧪 测试YAML解析...")
    
    try:
        import yaml
        
        with open("config/stocks_config.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("✅ YAML解析成功")
        
        # 验证结构
        if 'stocks' in config:
            stocks_config = config['stocks']
            print(f"   股票组数量: {len(stocks_config)}")
            
            for group_name, stock_list in stocks_config.items():
                if isinstance(stock_list, list):
                    print(f"   {group_name}: {len(stock_list)} 只股票")
        
        if 'subscription' in config:
            sub_config = config['subscription']
            print(f"   订阅配置: {sub_config}")
        
        return True
        
    except ImportError:
        print("⚠️  PyYAML未安装，跳过YAML解析测试")
        return True
    except Exception as e:
        print(f"❌ YAML解析失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始股票配置功能测试")
    print("=" * 50)
    
    tests = [
        ("配置文件内容", test_config_file_content),
        ("YAML解析", test_yaml_parsing),
        ("股票配置加载器", test_stock_config_loader),
        ("加载股票列表", test_load_stock_list),
        ("Producer集成", test_producer_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 股票配置功能测试全部通过！")
        print("\n📋 功能说明:")
        print("   ✅ 从config/stocks_config.yaml读取股票列表")
        print("   ✅ 支持多个股票组 (default_list, banking, technology, all)")
        print("   ✅ 可配置订阅参数 (period, count, delay)")
        print("   ✅ 支持过滤器功能")
        print("   ✅ 集成到producer主程序")
        print("\n🚀 可以启动服务测试:")
        print("   cd producer && python main.py")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置文件和代码")
        return 1


if __name__ == '__main__':
    exit(main())
