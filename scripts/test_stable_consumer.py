#!/usr/bin/env python3
"""
稳定性测试脚本
测试修复后的多线程消费者的稳定性和错误恢复能力
"""

import sys
import os
import time
import json
import requests
import threading
import subprocess
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class StabilityTester:
    """稳定性测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.health_url = "http://localhost:8081/health"
        self.metrics_url = "http://localhost:8001/metrics"
        self.test_duration = 300  # 5分钟测试
        self.check_interval = 10  # 10秒检查一次
        
        self.test_results = {
            'start_time': None,
            'end_time': None,
            'total_checks': 0,
            'healthy_checks': 0,
            'unhealthy_checks': 0,
            'connection_errors': 0,
            'delivery_tag_errors': 0,
            'reconnections': 0,
            'error_log': []
        }
    
    def check_consumer_health(self) -> dict:
        """检查消费者健康状态"""
        try:
            response = requests.get(self.health_url, timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return {'status': 'unhealthy', 'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}
    
    def get_consumer_metrics(self) -> dict:
        """获取消费者指标"""
        try:
            response = requests.get(self.metrics_url, timeout=5)
            if response.status_code == 200:
                metrics = {}
                for line in response.text.split('\n'):
                    if line.startswith('#') or not line.strip():
                        continue
                    
                    parts = line.split(' ')
                    if len(parts) >= 2:
                        metric_name = parts[0]
                        try:
                            metric_value = float(parts[1])
                            metrics[metric_name] = metric_value
                        except ValueError:
                            continue
                
                return metrics
            else:
                return {}
        except Exception as e:
            return {}
    
    def analyze_logs_for_errors(self) -> dict:
        """分析日志中的错误"""
        error_counts = {
            'delivery_tag_errors': 0,
            'connection_errors': 0,
            'transport_errors': 0,
            'channel_errors': 0,
            'reconnections': 0
        }
        
        try:
            log_file = "logs/threaded_consumer.log"
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 只分析最近的日志
                recent_lines = lines[-1000:] if len(lines) > 1000 else lines
                
                for line in recent_lines:
                    line_lower = line.lower()
                    
                    if 'delivery tag' in line_lower and 'unknown' in line_lower:
                        error_counts['delivery_tag_errors'] += 1
                    
                    if 'connection' in line_lower and ('lost' in line_lower or 'failed' in line_lower):
                        error_counts['connection_errors'] += 1
                    
                    if 'transport' in line_lower and 'error' in line_lower:
                        error_counts['transport_errors'] += 1
                    
                    if 'channel' in line_lower and ('close' in line_lower or 'error' in line_lower):
                        error_counts['channel_errors'] += 1
                    
                    if '重新连接' in line or 'reconnect' in line_lower:
                        error_counts['reconnections'] += 1
        
        except Exception as e:
            print(f"分析日志失败: {e}")
        
        return error_counts
    
    def simulate_network_issues(self):
        """模拟网络问题（可选）"""
        # 这里可以添加网络中断模拟
        # 例如：临时阻断RabbitMQ端口
        pass
    
    def run_stability_test(self):
        """运行稳定性测试"""
        print("🚀 开始稳定性测试")
        print("=" * 60)
        print(f"测试时长: {self.test_duration}秒")
        print(f"检查间隔: {self.check_interval}秒")
        print()
        
        self.test_results['start_time'] = datetime.now()
        start_time = time.time()
        
        # 获取初始指标
        initial_metrics = self.get_consumer_metrics()
        initial_errors = self.analyze_logs_for_errors()
        
        print("📊 初始状态:")
        health = self.check_consumer_health()
        print(f"   健康状态: {health.get('status', 'unknown')}")
        print(f"   消息处理: {initial_metrics.get('messages_processed_total', 0)}")
        print(f"   连接错误: {initial_errors['connection_errors']}")
        print(f"   Delivery Tag错误: {initial_errors['delivery_tag_errors']}")
        print()
        
        # 开始监控循环
        while time.time() - start_time < self.test_duration:
            try:
                self.test_results['total_checks'] += 1
                
                # 检查健康状态
                health = self.check_consumer_health()
                metrics = self.get_consumer_metrics()
                
                if health.get('status') == 'healthy':
                    self.test_results['healthy_checks'] += 1
                    status_icon = "✅"
                else:
                    self.test_results['unhealthy_checks'] += 1
                    status_icon = "❌"
                    self.test_results['error_log'].append({
                        'time': datetime.now().isoformat(),
                        'error': health.get('error', 'Unknown error')
                    })
                
                # 显示当前状态
                elapsed = time.time() - start_time
                remaining = self.test_duration - elapsed
                
                print(f"{status_icon} [{elapsed:6.0f}s] 健康: {health.get('status', 'unknown'):8} | "
                      f"消息: {metrics.get('messages_processed_total', 0):6.0f} | "
                      f"队列: {metrics.get('consumer_queue_depth', 0):3.0f} | "
                      f"剩余: {remaining:3.0f}s")
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                print("\n测试被用户中断")
                break
            except Exception as e:
                print(f"检查过程中发生错误: {e}")
                time.sleep(1)
        
        self.test_results['end_time'] = datetime.now()
        
        # 获取最终指标
        final_metrics = self.get_consumer_metrics()
        final_errors = self.analyze_logs_for_errors()
        
        # 计算差值
        messages_processed = final_metrics.get('messages_processed_total', 0) - initial_metrics.get('messages_processed_total', 0)
        new_connection_errors = final_errors['connection_errors'] - initial_errors['connection_errors']
        new_delivery_tag_errors = final_errors['delivery_tag_errors'] - initial_errors['delivery_tag_errors']
        new_reconnections = final_errors['reconnections'] - initial_errors['reconnections']
        
        # 生成报告
        self.generate_report(messages_processed, new_connection_errors, new_delivery_tag_errors, new_reconnections)
    
    def generate_report(self, messages_processed, connection_errors, delivery_tag_errors, reconnections):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 稳定性测试报告")
        print("=" * 60)
        
        # 基本统计
        duration = (self.test_results['end_time'] - self.test_results['start_time']).total_seconds()
        uptime_percentage = (self.test_results['healthy_checks'] / self.test_results['total_checks']) * 100 if self.test_results['total_checks'] > 0 else 0
        
        print(f"测试时长: {duration:.0f}秒")
        print(f"总检查次数: {self.test_results['total_checks']}")
        print(f"健康检查: {self.test_results['healthy_checks']}")
        print(f"异常检查: {self.test_results['unhealthy_checks']}")
        print(f"可用性: {uptime_percentage:.1f}%")
        print()
        
        # 性能统计
        print("📈 性能统计:")
        print(f"处理消息数: {messages_processed}")
        if duration > 0:
            print(f"消息处理速率: {messages_processed / duration:.1f} msg/s")
        print()
        
        # 错误统计
        print("🔍 错误统计:")
        print(f"连接错误: {connection_errors}")
        print(f"Delivery Tag错误: {delivery_tag_errors}")
        print(f"重连次数: {reconnections}")
        print()
        
        # 稳定性评估
        print("🎯 稳定性评估:")
        
        if uptime_percentage >= 95:
            stability_score = "优秀"
            stability_icon = "🟢"
        elif uptime_percentage >= 85:
            stability_score = "良好"
            stability_icon = "🟡"
        else:
            stability_score = "需要改进"
            stability_icon = "🔴"
        
        print(f"{stability_icon} 总体稳定性: {stability_score} ({uptime_percentage:.1f}%)")
        
        if delivery_tag_errors == 0:
            print("✅ Delivery Tag问题: 已修复")
        else:
            print(f"❌ Delivery Tag问题: 仍存在 ({delivery_tag_errors}个错误)")
        
        if connection_errors <= 2:
            print("✅ 连接稳定性: 良好")
        else:
            print(f"⚠️  连接稳定性: 需要关注 ({connection_errors}个错误)")
        
        if reconnections <= 3:
            print("✅ 自动恢复: 正常")
        else:
            print(f"⚠️  自动恢复: 频繁重连 ({reconnections}次)")
        
        # 建议
        print("\n💡 建议:")
        if delivery_tag_errors > 0:
            print("   - 检查delivery tag处理逻辑")
            print("   - 验证消息确认机制")
        
        if connection_errors > 5:
            print("   - 检查网络稳定性")
            print("   - 调整连接参数")
            print("   - 增加重连间隔")
        
        if uptime_percentage < 90:
            print("   - 分析错误日志")
            print("   - 优化错误处理机制")
            print("   - 考虑增加监控告警")
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"stability_test_report_{timestamp}.json"
        
        report_data = {
            'test_results': self.test_results,
            'performance': {
                'messages_processed': messages_processed,
                'duration': duration,
                'message_rate': messages_processed / duration if duration > 0 else 0
            },
            'errors': {
                'connection_errors': connection_errors,
                'delivery_tag_errors': delivery_tag_errors,
                'reconnections': reconnections
            },
            'stability': {
                'uptime_percentage': uptime_percentage,
                'score': stability_score
            }
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 详细报告已保存到: {report_file}")


def main():
    """主函数"""
    print("🔧 多线程消费者稳定性测试工具")
    print("=" * 60)
    print("请确保多线程消费者正在运行:")
    print("python consumer/threaded_main.py --env dev")
    print()
    
    # 等待用户确认
    try:
        input("消费者启动后按回车开始测试...")
    except KeyboardInterrupt:
        print("\n测试被用户取消")
        return
    
    # 运行测试
    tester = StabilityTester()
    
    try:
        tester.run_stability_test()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == '__main__':
    main()
