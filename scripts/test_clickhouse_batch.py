#!/usr/bin/env python3
"""
ClickHouse批量插入测试脚本
测试和验证ClickHouse批量插入功能的性能和配置
"""

import sys
import os
import time
import json
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from consumer.clickhouse_manager import ClickHouseManager


class ClickHouseBatchTester:
    """ClickHouse批量插入测试器"""
    
    def __init__(self, config_path: str = "config/dev/database.yaml"):
        """初始化测试器"""
        self.config_path = config_path
        self.ch_manager = None
        
    def setup(self):
        """设置测试环境"""
        try:
            self.ch_manager = ClickHouseManager(self.config_path)
            print("✅ ClickHouse管理器初始化成功")
            return True
        except Exception as e:
            print(f"❌ ClickHouse管理器初始化失败: {e}")
            return False
    
    def generate_test_data(self, count: int) -> List[Dict[str, Any]]:
        """生成测试数据"""
        test_data = []
        symbols = ['600030.SH', '000776.SZ', '600999.SH', '002736.SZ', '300059.SZ']
        
        for i in range(count):
            symbol = random.choice(symbols)
            timestamp = datetime.now() - timedelta(seconds=random.randint(0, 3600))
            
            quote_data = {
                'symbol': symbol,
                'quote_data': {
                    'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    'last_price': round(random.uniform(10.0, 100.0), 2),
                    'volume': random.randint(1000, 100000),
                    'turnover': round(random.uniform(100000, 10000000), 2),
                    'high': round(random.uniform(50.0, 120.0), 2),
                    'low': round(random.uniform(5.0, 50.0), 2),
                    'open': round(random.uniform(20.0, 80.0), 2),
                    'prev_close': round(random.uniform(20.0, 80.0), 2),
                    'bid_price': [round(random.uniform(10.0, 100.0), 2) for _ in range(5)],
                    'bid_volume': [random.randint(100, 10000) for _ in range(5)],
                    'ask_price': [round(random.uniform(10.0, 100.0), 2) for _ in range(5)],
                    'ask_volume': [random.randint(100, 10000) for _ in range(5)]
                }
            }
            test_data.append(quote_data)
        
        return test_data
    
    def test_direct_batch_insert(self, data_count: int = 1000):
        """测试直接批量插入"""
        print(f"\n🔧 测试直接批量插入 ({data_count} 条记录)")
        print("-" * 50)
        
        # 生成测试数据
        test_data = self.generate_test_data(data_count)
        print(f"✅ 生成 {len(test_data)} 条测试数据")
        
        # 执行批量插入
        start_time = time.time()
        success = self.ch_manager._direct_batch_insert(test_data)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if success:
            print(f"✅ 直接批量插入成功")
            print(f"   耗时: {duration:.2f} 秒")
            print(f"   速率: {data_count / duration:.0f} 记录/秒")
        else:
            print(f"❌ 直接批量插入失败")
        
        return success, duration
    
    def test_buffered_batch_insert(self, data_count: int = 1000, batch_size: int = 100):
        """测试缓冲批量插入"""
        print(f"\n🔧 测试缓冲批量插入 ({data_count} 条记录, 批次大小: {batch_size})")
        print("-" * 50)
        
        # 临时修改批次大小
        original_batch_size = self.ch_manager.batch_size
        self.ch_manager.batch_size = batch_size
        
        try:
            # 生成测试数据
            test_data = self.generate_test_data(data_count)
            print(f"✅ 生成 {len(test_data)} 条测试数据")
            
            # 分批插入
            start_time = time.time()
            success_count = 0
            
            for i in range(0, len(test_data), batch_size):
                batch = test_data[i:i + batch_size]
                if self.ch_manager._batch_insert_with_buffer(batch):
                    success_count += len(batch)
                time.sleep(0.1)  # 模拟实际处理间隔
            
            # 强制刷新剩余数据
            self.ch_manager.force_flush()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✅ 缓冲批量插入完成")
            print(f"   成功记录: {success_count}/{data_count}")
            print(f"   耗时: {duration:.2f} 秒")
            print(f"   速率: {success_count / duration:.0f} 记录/秒")
            
            return True, duration
            
        finally:
            # 恢复原始批次大小
            self.ch_manager.batch_size = original_batch_size
    
    def test_parallel_insert(self, data_count: int = 2000):
        """测试并行插入"""
        print(f"\n🔧 测试并行插入 ({data_count} 条记录)")
        print("-" * 50)
        
        # 临时启用并行插入
        original_parallel = self.ch_manager.batch_parallel_inserts
        self.ch_manager.batch_parallel_inserts = 4
        
        try:
            # 生成测试数据
            test_data = self.generate_test_data(data_count)
            print(f"✅ 生成 {len(test_data)} 条测试数据")
            
            # 执行并行插入
            start_time = time.time()
            success = self.ch_manager._direct_batch_insert(test_data)
            end_time = time.time()
            
            duration = end_time - start_time
            
            if success:
                print(f"✅ 并行插入成功")
                print(f"   耗时: {duration:.2f} 秒")
                print(f"   速率: {data_count / duration:.0f} 记录/秒")
                print(f"   并行线程数: {self.ch_manager.batch_parallel_inserts}")
            else:
                print(f"❌ 并行插入失败")
            
            return success, duration
            
        finally:
            # 恢复原始设置
            self.ch_manager.batch_parallel_inserts = original_parallel
    
    def test_compression_impact(self, data_count: int = 1000):
        """测试压缩对性能的影响"""
        print(f"\n🔧 测试压缩影响 ({data_count} 条记录)")
        print("-" * 50)
        
        results = {}
        
        for compression in [False, True]:
            print(f"\n测试压缩设置: {compression}")
            
            # 设置压缩
            original_compression = self.ch_manager.batch_compression
            self.ch_manager.batch_compression = compression
            
            try:
                # 生成测试数据
                test_data = self.generate_test_data(data_count)
                
                # 执行插入
                start_time = time.time()
                success = self.ch_manager._direct_batch_insert(test_data)
                end_time = time.time()
                
                duration = end_time - start_time
                results[f"compression_{compression}"] = {
                    'success': success,
                    'duration': duration,
                    'rate': data_count / duration if duration > 0 else 0
                }
                
                print(f"   耗时: {duration:.2f} 秒")
                print(f"   速率: {data_count / duration:.0f} 记录/秒")
                
            finally:
                # 恢复原始设置
                self.ch_manager.batch_compression = original_compression
        
        # 比较结果
        if 'compression_False' in results and 'compression_True' in results:
            no_comp = results['compression_False']
            with_comp = results['compression_True']
            
            print(f"\n📊 压缩影响分析:")
            print(f"   无压缩: {no_comp['rate']:.0f} 记录/秒")
            print(f"   有压缩: {with_comp['rate']:.0f} 记录/秒")
            
            if with_comp['rate'] > no_comp['rate']:
                improvement = (with_comp['rate'] / no_comp['rate'] - 1) * 100
                print(f"   压缩提升: +{improvement:.1f}%")
            else:
                degradation = (1 - with_comp['rate'] / no_comp['rate']) * 100
                print(f"   压缩影响: -{degradation:.1f}%")
        
        return results
    
    def show_batch_stats(self):
        """显示批量插入统计信息"""
        print(f"\n📊 批量插入统计信息")
        print("-" * 50)
        
        stats = self.ch_manager.get_batch_stats()
        
        print(f"总批次数: {stats['total_batches']}")
        print(f"总记录数: {stats['total_records']}")
        print(f"总字节数: {stats['total_bytes']:,}")
        print(f"平均批次时间: {stats['avg_batch_time']:.3f} 秒")
        
        if stats['last_batch_time']:
            last_time = datetime.fromtimestamp(stats['last_batch_time'])
            print(f"最后批次时间: {last_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"\n当前缓冲区:")
        buffer = stats['current_buffer_size']
        print(f"   行情数据: {buffer['quotes']}")
        print(f"   盘口数据: {buffer['depth']}")
        print(f"   日志数据: {buffer['logs']}")
        
        print(f"\n批次配置:")
        config = stats['batch_config']
        print(f"   批次大小: {config['batch_size']}")
        print(f"   批次超时: {config['batch_timeout']} 秒")
        print(f"   自动刷新: {config['auto_flush']}")
        print(f"   压缩: {config['compression']}")
        print(f"   并行插入: {config['parallel_inserts']}")
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 ClickHouse批量插入综合测试")
        print("=" * 60)
        
        if not self.setup():
            return False
        
        try:
            # 测试1: 直接批量插入
            direct_success, direct_time = self.test_direct_batch_insert(1000)
            
            # 测试2: 缓冲批量插入
            buffered_success, buffered_time = self.test_buffered_batch_insert(1000, 100)
            
            # 测试3: 并行插入
            parallel_success, parallel_time = self.test_parallel_insert(2000)
            
            # 测试4: 压缩影响
            compression_results = self.test_compression_impact(1000)
            
            # 显示统计信息
            self.show_batch_stats()
            
            # 总结
            print(f"\n🎯 测试总结")
            print("=" * 60)
            print(f"直接批量插入: {'✅' if direct_success else '❌'} ({direct_time:.2f}s)")
            print(f"缓冲批量插入: {'✅' if buffered_success else '❌'} ({buffered_time:.2f}s)")
            print(f"并行插入: {'✅' if parallel_success else '❌'} ({parallel_time:.2f}s)")
            
            # 性能建议
            print(f"\n💡 性能建议:")
            if parallel_time < direct_time:
                print("   - 启用并行插入可以提高性能")
            if buffered_time < direct_time:
                print("   - 使用缓冲批量插入可以提高效率")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {e}")
            return False
        finally:
            if self.ch_manager:
                self.ch_manager.close()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='ClickHouse批量插入测试工具')
    parser.add_argument('--config', default='config/dev/database.yaml', 
                       help='数据库配置文件路径')
    parser.add_argument('--test-type', choices=['all', 'direct', 'buffered', 'parallel', 'compression'],
                       default='all', help='测试类型')
    parser.add_argument('--data-count', type=int, default=1000, help='测试数据数量')
    
    args = parser.parse_args()
    
    tester = ClickHouseBatchTester(args.config)
    
    try:
        if args.test_type == 'all':
            success = tester.run_comprehensive_test()
        else:
            if not tester.setup():
                return 1
            
            if args.test_type == 'direct':
                success, _ = tester.test_direct_batch_insert(args.data_count)
            elif args.test_type == 'buffered':
                success, _ = tester.test_buffered_batch_insert(args.data_count)
            elif args.test_type == 'parallel':
                success, _ = tester.test_parallel_insert(args.data_count)
            elif args.test_type == 'compression':
                results = tester.test_compression_impact(args.data_count)
                success = any(r['success'] for r in results.values())
            
            tester.show_batch_stats()
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
