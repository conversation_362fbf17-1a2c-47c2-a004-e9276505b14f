#!/usr/bin/env python3
"""
分区初始化脚本
用于初始化数据库分区表和相关存储过程
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from consumer.database_manager import get_database_manager


class PartitionInitializer:
    """分区初始化器"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """初始化"""
        self.db_manager = get_database_manager(config_path)
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def check_partition_support(self) -> bool:
        """检查数据库是否支持分区"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查分区插件是否启用
                cursor.execute("SHOW PLUGINS")
                plugins = cursor.fetchall()
                
                partition_enabled = any(
                    plugin['Name'] == 'partition' and plugin['Status'] == 'ACTIVE'
                    for plugin in plugins
                )
                
                if partition_enabled:
                    self.logger.info("✅ 数据库支持分区功能")
                    return True
                else:
                    self.logger.error("❌ 数据库不支持分区功能")
                    return False
                    
        except Exception as e:
            self.logger.error(f"检查分区支持失败: {e}")
            return False
    
    def check_existing_partitions(self) -> dict:
        """检查现有分区"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 查询现有分区
                cursor.execute("""
                    SELECT TABLE_NAME, COUNT(*) as partition_count
                    FROM INFORMATION_SCHEMA.PARTITIONS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME IN ('market_quotes', 'market_depth', 'processing_log')
                    AND PARTITION_NAME IS NOT NULL
                    GROUP BY TABLE_NAME
                """)
                
                existing = {row['TABLE_NAME']: row['partition_count'] for row in cursor.fetchall()}
                
                for table in ['market_quotes', 'market_depth', 'processing_log']:
                    count = existing.get(table, 0)
                    if count > 0:
                        self.logger.info(f"📊 {table}: 已有 {count} 个分区")
                    else:
                        self.logger.info(f"📊 {table}: 未分区")
                
                return existing
                
        except Exception as e:
            self.logger.error(f"检查现有分区失败: {e}")
            return {}
    
    def check_procedures(self) -> bool:
        """检查存储过程是否存在"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查存储过程
                cursor.execute("""
                    SELECT ROUTINE_NAME 
                    FROM INFORMATION_SCHEMA.ROUTINES
                    WHERE ROUTINE_SCHEMA = DATABASE()
                    AND ROUTINE_TYPE = 'PROCEDURE'
                    AND ROUTINE_NAME IN ('CreateDailyPartitions', 'CleanHistoryPartitions', 'MaintainPartitions')
                """)
                
                procedures = [row['ROUTINE_NAME'] for row in cursor.fetchall()]
                required_procedures = ['CreateDailyPartitions', 'CleanHistoryPartitions', 'MaintainPartitions']
                
                missing = set(required_procedures) - set(procedures)
                
                if missing:
                    self.logger.warning(f"⚠️  缺少存储过程: {', '.join(missing)}")
                    return False
                else:
                    self.logger.info("✅ 所有分区管理存储过程已存在")
                    return True
                    
        except Exception as e:
            self.logger.error(f"检查存储过程失败: {e}")
            return False
    
    def check_event_scheduler(self) -> bool:
        """检查事件调度器状态"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查事件调度器状态
                cursor.execute("SHOW VARIABLES LIKE 'event_scheduler'")
                result = cursor.fetchone()
                
                if result and result['Value'] == 'ON':
                    self.logger.info("✅ 事件调度器已启用")
                    
                    # 检查自动维护事件
                    cursor.execute("""
                        SELECT EVENT_NAME, STATUS 
                        FROM INFORMATION_SCHEMA.EVENTS
                        WHERE EVENT_SCHEMA = DATABASE()
                        AND EVENT_NAME = 'auto_partition_maintenance'
                    """)
                    
                    event = cursor.fetchone()
                    if event:
                        self.logger.info(f"✅ 自动分区维护事件: {event['STATUS']}")
                    else:
                        self.logger.warning("⚠️  自动分区维护事件不存在")
                    
                    return True
                else:
                    self.logger.warning("⚠️  事件调度器未启用")
                    return False
                    
        except Exception as e:
            self.logger.error(f"检查事件调度器失败: {e}")
            return False
    
    def create_initial_partitions(self, days_ahead: int = 7) -> bool:
        """创建初始分区"""
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                self.logger.info(f"🔧 创建未来 {days_ahead} 天的分区...")
                
                # 调用存储过程创建分区
                cursor.callproc('CreateDailyPartitions', [days_ahead])
                conn.commit()
                
                self.logger.info("✅ 初始分区创建完成")
                return True
                
        except Exception as e:
            self.logger.error(f"创建初始分区失败: {e}")
            return False
    
    def run_full_check(self) -> bool:
        """运行完整检查"""
        self.logger.info("🚀 开始分区系统检查...")
        self.logger.info("=" * 60)
        
        # 1. 检查分区支持
        if not self.check_partition_support():
            return False
        
        # 2. 检查现有分区
        existing_partitions = self.check_existing_partitions()
        
        # 3. 检查存储过程
        procedures_ok = self.check_procedures()
        
        # 4. 检查事件调度器
        scheduler_ok = self.check_event_scheduler()
        
        # 5. 如果没有分区，创建初始分区
        if not any(existing_partitions.values()):
            if procedures_ok:
                self.logger.info("🔧 检测到未分区表，开始创建初始分区...")
                if not self.create_initial_partitions():
                    return False
            else:
                self.logger.error("❌ 存储过程不存在，无法创建分区")
                return False
        
        self.logger.info("=" * 60)
        self.logger.info("📋 检查结果汇总:")
        self.logger.info(f"   分区支持: ✅")
        self.logger.info(f"   存储过程: {'✅' if procedures_ok else '❌'}")
        self.logger.info(f"   事件调度器: {'✅' if scheduler_ok else '⚠️'}")
        
        for table in ['market_quotes', 'market_depth', 'processing_log']:
            count = existing_partitions.get(table, 0)
            status = "✅" if count > 0 else "❌"
            self.logger.info(f"   {table}: {status} ({count} 个分区)")
        
        all_ok = procedures_ok and all(existing_partitions.values())
        
        if all_ok:
            self.logger.info("🎉 分区系统检查通过！")
        else:
            self.logger.warning("⚠️  分区系统存在问题，请检查上述输出")
        
        return all_ok


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='分区系统初始化和检查工具')
    parser.add_argument('--config', default='config/database.yaml', help='数据库配置文件路径')
    parser.add_argument('--create-partitions', action='store_true', help='强制创建初始分区')
    parser.add_argument('--days', type=int, default=7, help='创建多少天的分区')
    
    args = parser.parse_args()
    
    # 创建初始化器
    initializer = PartitionInitializer(args.config)
    
    try:
        if args.create_partitions:
            # 强制创建分区
            success = initializer.create_initial_partitions(args.days)
            return 0 if success else 1
        else:
            # 运行完整检查
            success = initializer.run_full_check()
            return 0 if success else 1
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 1
    except Exception as e:
        print(f"操作失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
