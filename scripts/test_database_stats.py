#!/usr/bin/env python3
"""
数据库统计信息测试脚本
用于测试数据库管理器的统计功能
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_database_stats():
    """测试数据库统计信息"""
    try:
        from consumer.database_factory import get_database_manager
        
        print("🔗 测试数据库管理器统计信息...")
        
        # 获取数据库管理器
        db_manager = get_database_manager("config/database.yaml")
        
        print(f"✅ 数据库管理器类型: {type(db_manager).__name__}")
        print(f"✅ 数据库类型: {db_manager.get_database_type()}")
        
        # 测试连接池大小
        try:
            pool_size = db_manager.get_pool_size()
            print(f"✅ 连接池大小: {pool_size}")
        except Exception as e:
            print(f"❌ 获取连接池大小失败: {e}")
        
        # 测试健康检查
        try:
            health = db_manager.health_check()
            print(f"✅ 健康状态: {health.get('status', 'unknown')}")
            if health.get('status') != 'healthy':
                print(f"   错误信息: {health.get('error', 'N/A')}")
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
        
        # 测试统计信息
        try:
            stats = db_manager.get_stats()
            print(f"✅ 统计信息:")
            for key, value in stats.items():
                if key == 'health':
                    print(f"   {key}: {value.get('status', 'unknown')}")
                else:
                    print(f"   {key}: {value}")
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_dual_database_manager():
    """测试双数据库管理器"""
    try:
        import yaml
        from consumer.database_factory import DualDatabaseManager
        
        print("\n🔗 测试双数据库管理器...")
        
        # 创建临时配置
        config_path = "config/database.yaml"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 备份原配置
        original_dual_write = config.get('dual_write', {}).copy()
        
        # 设置双写模式（但不启用，避免连接问题）
        config['dual_write'] = {
            'enabled': False,
            'primary': 'mysql',
            'secondary': 'clickhouse',
            'fail_on_secondary_error': False
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        # 测试双数据库管理器
        dual_manager = DualDatabaseManager(config_path)
        
        print(f"✅ 双数据库管理器类型: {type(dual_manager).__name__}")
        print(f"✅ 双写模式启用: {dual_manager.enabled}")
        print(f"✅ 数据库类型: {dual_manager.get_database_type()}")
        
        # 测试连接池大小
        try:
            pool_size = dual_manager.get_pool_size()
            print(f"✅ 连接池大小: {pool_size}")
        except Exception as e:
            print(f"❌ 获取连接池大小失败: {e}")
        
        # 恢复原配置
        config['dual_write'] = original_dual_write
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 双数据库管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    setup_logging()
    
    print("🚀 数据库统计信息测试")
    print("=" * 50)
    
    # 测试单数据库管理器
    success1 = test_database_stats()
    
    # 测试双数据库管理器
    success2 = test_dual_database_manager()
    
    print("\n" + "=" * 50)
    print("📋 测试结果:")
    print(f"   单数据库管理器: {'✅' if success1 else '❌'}")
    print(f"   双数据库管理器: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！数据库统计功能正常")
        return 0
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return 1

if __name__ == '__main__':
    exit(main())
