#!/usr/bin/env python3
"""
分区管理脚本
用于管理 market_quotes, market_depth, processing_log 表的日分区
"""

import sys
import os
import logging
import argparse
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from consumer.database_manager import get_database_manager


class PartitionManager:
    """分区管理器"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """初始化分区管理器"""
        self.db_manager = get_database_manager(config_path)
        self.logger = self._setup_logger()
        
        # 需要管理分区的表
        self.partitioned_tables = ['market_quotes', 'market_depth', 'processing_log']
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def create_partitions(self, days_ahead: int = 7) -> bool:
        """
        创建未来几天的分区
        
        Args:
            days_ahead: 提前创建多少天的分区
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                self.logger.info(f"开始创建未来 {days_ahead} 天的分区...")
                
                # 调用存储过程创建分区
                cursor.callproc('CreateDailyPartitions', [days_ahead])
                conn.commit()
                
                self.logger.info(f"成功创建未来 {days_ahead} 天的分区")
                return True
                
        except Exception as e:
            self.logger.error(f"创建分区失败: {e}")
            return False
    
    def clean_partitions(self, days_to_keep: int = 30) -> bool:
        """
        清理历史分区
        
        Args:
            days_to_keep: 保留多少天的数据
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                self.logger.info(f"开始清理 {days_to_keep} 天前的历史分区...")
                
                # 调用存储过程清理分区
                cursor.callproc('CleanHistoryPartitions', [days_to_keep])
                conn.commit()
                
                self.logger.info(f"成功清理 {days_to_keep} 天前的历史分区")
                return True
                
        except Exception as e:
            self.logger.error(f"清理分区失败: {e}")
            return False
    
    def maintain_partitions(self, days_to_keep: int = 30, days_ahead: int = 7) -> bool:
        """
        维护分区（创建未来分区 + 清理历史分区）
        
        Args:
            days_to_keep: 保留多少天的数据
            days_ahead: 提前创建多少天的分区
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                self.logger.info(f"开始维护分区: 保留{days_to_keep}天, 提前创建{days_ahead}天")
                
                # 调用存储过程维护分区
                cursor.callproc('MaintainPartitions', [days_to_keep, days_ahead])
                conn.commit()
                
                self.logger.info("分区维护完成")
                return True
                
        except Exception as e:
            self.logger.error(f"维护分区失败: {e}")
            return False
    
    def list_partitions(self) -> List[Dict[str, Any]]:
        """
        列出所有分区信息
        
        Returns:
            List[Dict]: 分区信息列表
        """
        try:
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 查询分区状态视图
                cursor.execute("SELECT * FROM partition_status ORDER BY TABLE_NAME, PARTITION_NAME")
                partitions = cursor.fetchall()
                
                return partitions
                
        except Exception as e:
            self.logger.error(f"查询分区信息失败: {e}")
            return []
    
    def show_partition_status(self):
        """显示分区状态"""
        partitions = self.list_partitions()
        
        if not partitions:
            self.logger.warning("没有找到分区信息")
            return
        
        print("\n" + "="*100)
        print("分区状态报告")
        print("="*100)
        
        current_table = None
        total_rows = 0
        total_data_size = 0
        
        for partition in partitions:
            table_name = partition['TABLE_NAME']
            partition_name = partition['PARTITION_NAME']
            rows = partition['TABLE_ROWS'] or 0
            data_length = partition['DATA_LENGTH'] or 0
            index_length = partition['INDEX_LENGTH'] or 0
            
            if current_table != table_name:
                if current_table is not None:
                    print(f"  小计: {total_rows:,} 行, {self._format_size(total_data_size)}")
                    print("-" * 80)
                
                current_table = table_name
                total_rows = 0
                total_data_size = 0
                print(f"\n📊 {table_name}")
                print("-" * 80)
            
            total_rows += rows
            total_data_size += data_length + index_length
            
            print(f"  {partition_name}: {rows:,} 行, {self._format_size(data_length + index_length)}")
        
        if current_table is not None:
            print(f"  小计: {total_rows:,} 行, {self._format_size(total_data_size)}")
        
        print("="*100)
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分区管理工具')
    parser.add_argument('--config', default='config/database.yaml', help='数据库配置文件路径')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 创建分区命令
    create_parser = subparsers.add_parser('create', help='创建未来分区')
    create_parser.add_argument('--days', type=int, default=7, help='提前创建多少天的分区')
    
    # 清理分区命令
    clean_parser = subparsers.add_parser('clean', help='清理历史分区')
    clean_parser.add_argument('--keep', type=int, default=30, help='保留多少天的数据')
    
    # 维护分区命令
    maintain_parser = subparsers.add_parser('maintain', help='维护分区')
    maintain_parser.add_argument('--keep', type=int, default=30, help='保留多少天的数据')
    maintain_parser.add_argument('--ahead', type=int, default=7, help='提前创建多少天的分区')
    
    # 查看分区状态命令
    subparsers.add_parser('status', help='查看分区状态')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # 创建分区管理器
    manager = PartitionManager(args.config)
    
    try:
        if args.command == 'create':
            success = manager.create_partitions(args.days)
            return 0 if success else 1
            
        elif args.command == 'clean':
            success = manager.clean_partitions(args.keep)
            return 0 if success else 1
            
        elif args.command == 'maintain':
            success = manager.maintain_partitions(args.keep, args.ahead)
            return 0 if success else 1
            
        elif args.command == 'status':
            manager.show_partition_status()
            return 0
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 1
    except Exception as e:
        print(f"操作失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
