#!/usr/bin/env python3
"""
快速测试脚本
用于验证数据库连接和基本功能
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_mysql_connection():
    """测试MySQL连接"""
    print("🔗 测试MySQL连接...")
    try:
        # 临时修改配置为MySQL
        import yaml
        config_path = "config/database.yaml"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 备份原配置
        original_type = config.get('database_type')
        
        # 设置为MySQL
        config['database_type'] = 'mysql'
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        # 测试连接
        from consumer.database_factory import get_database_manager
        
        db_manager = get_database_manager(config_path)
        health = db_manager.health_check()
        
        if health['status'] == 'healthy':
            print("✅ MySQL连接成功")
            print(f"   数据库类型: {health.get('database_type', 'N/A')}")
            result = True
        else:
            print(f"❌ MySQL连接失败: {health.get('error', '未知错误')}")
            result = False
        
        # 恢复原配置
        config['database_type'] = original_type
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return result
        
    except Exception as e:
        print(f"❌ MySQL测试失败: {e}")
        return False

def test_clickhouse_connection():
    """测试ClickHouse连接"""
    print("🔗 测试ClickHouse连接...")
    try:
        # 检查ClickHouse依赖
        try:
            from clickhouse_driver import Client
        except ImportError:
            print("❌ ClickHouse驱动未安装，请运行: pip install clickhouse-driver")
            return False
        
        # 临时修改配置为ClickHouse
        import yaml
        config_path = "config/database.yaml"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 备份原配置
        original_type = config.get('database_type')
        
        # 设置为ClickHouse
        config['database_type'] = 'clickhouse'
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        # 测试连接
        from consumer.database_factory import get_database_manager
        
        db_manager = get_database_manager(config_path)
        health = db_manager.health_check()
        
        if health['status'] == 'healthy':
            print("✅ ClickHouse连接成功")
            print(f"   数据库类型: {health.get('database_type', 'N/A')}")
            result = True
        else:
            print(f"❌ ClickHouse连接失败: {health.get('error', '未知错误')}")
            result = False
        
        # 恢复原配置
        config['database_type'] = original_type
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return result
        
    except Exception as e:
        print(f"❌ ClickHouse测试失败: {e}")
        return False

def test_dual_write_mode():
    """测试双写模式"""
    print("🔗 测试双写模式...")
    try:
        import yaml
        config_path = "config/database.yaml"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 备份原配置
        original_dual_write = config.get('dual_write', {}).copy()
        
        # 设置双写模式
        config['dual_write'] = {
            'enabled': True,
            'primary': 'mysql',
            'secondary': 'clickhouse',
            'fail_on_secondary_error': False
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        # 测试双写模式
        from consumer.database_factory import DualDatabaseManager
        
        dual_manager = DualDatabaseManager(config_path)
        health = dual_manager.health_check()
        
        if health.get('overall_status') == 'healthy':
            print("✅ 双写模式连接成功")
            print(f"   主数据库: {health['primary']['type']}")
            print(f"   辅助数据库: {health['secondary']['type']}")
            result = True
        else:
            print(f"❌ 双写模式连接失败")
            print(f"   主数据库状态: {health['primary']['health'].get('status', 'unknown')}")
            print(f"   辅助数据库状态: {health['secondary']['health'].get('status', 'unknown')}")
            result = False
        
        # 恢复原配置
        config['dual_write'] = original_dual_write
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return result
        
    except Exception as e:
        print(f"❌ 双写模式测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    print("📦 检查依赖包...")
    
    dependencies = [
        ('PyMySQL', 'pymysql'),
        ('cryptography', 'cryptography'),
        ('ClickHouse Driver', 'clickhouse_driver'),
        ('PyYAML', 'yaml'),
        ('Pika', 'pika')
    ]
    
    missing = []
    
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} (缺失)")
            missing.append(name)
    
    if missing:
        print(f"\n⚠️  缺失依赖: {', '.join(missing)}")
        print("请运行以下命令安装:")
        if 'cryptography' in missing:
            print("   pip install cryptography")
        if 'ClickHouse Driver' in missing:
            print("   pip install clickhouse-driver")
        return False
    else:
        print("✅ 所有依赖包已安装")
        return True

def main():
    """主函数"""
    setup_logging()
    
    print("🚀 开始快速测试...")
    print("=" * 50)
    
    # 检查依赖
    deps_ok = check_dependencies()
    print("-" * 50)
    
    if not deps_ok:
        print("❌ 依赖检查失败，请先安装缺失的依赖包")
        return 1
    
    # 测试MySQL连接
    mysql_ok = test_mysql_connection()
    print("-" * 50)
    
    # 测试ClickHouse连接
    clickhouse_ok = test_clickhouse_connection()
    print("-" * 50)
    
    # 如果两个数据库都可用，测试双写模式
    if mysql_ok and clickhouse_ok:
        dual_ok = test_dual_write_mode()
        print("-" * 50)
    else:
        dual_ok = False
        print("⚠️  跳过双写模式测试（需要MySQL和ClickHouse都可用）")
        print("-" * 50)
    
    # 总结
    print("📋 测试结果总结:")
    print(f"   依赖包: {'✅' if deps_ok else '❌'}")
    print(f"   MySQL: {'✅' if mysql_ok else '❌'}")
    print(f"   ClickHouse: {'✅' if clickhouse_ok else '❌'}")
    print(f"   双写模式: {'✅' if dual_ok else '❌'}")
    
    if mysql_ok or clickhouse_ok:
        print("\n🎉 至少一个数据库可用，系统可以正常运行！")
        
        if mysql_ok and not clickhouse_ok:
            print("💡 建议: 配置 database_type: mysql")
        elif clickhouse_ok and not mysql_ok:
            print("💡 建议: 配置 database_type: clickhouse")
        elif mysql_ok and clickhouse_ok:
            print("💡 建议: 可以使用双写模式或选择任一数据库")
        
        return 0
    else:
        print("\n❌ 没有可用的数据库连接，请检查配置和服务状态")
        return 1

if __name__ == '__main__':
    exit(main())
