#!/usr/bin/env python3
"""
性能测试脚本
对比单线程和多线程消费者的性能
"""

import sys
import os
import time
import json
import threading
import argparse
from datetime import datetime, timedelta
from typing import Dict, Any, List
import requests

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, metrics_url: str):
        """
        初始化性能监控器
        
        Args:
            metrics_url: Prometheus指标URL
        """
        self.metrics_url = metrics_url
        self.start_time = None
        self.baseline_stats = None
        
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.baseline_stats = self.get_current_stats()
        
    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前统计信息"""
        try:
            response = requests.get(self.metrics_url, timeout=5)
            if response.status_code == 200:
                # 解析Prometheus指标
                metrics = {}
                for line in response.text.split('\n'):
                    if line.startswith('#') or not line.strip():
                        continue
                    
                    parts = line.split(' ')
                    if len(parts) >= 2:
                        metric_name = parts[0]
                        metric_value = float(parts[1])
                        metrics[metric_name] = metric_value
                
                return metrics
            else:
                return {}
        except Exception as e:
            print(f"获取指标失败: {e}")
            return {}
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.start_time or not self.baseline_stats:
            return {}
        
        current_stats = self.get_current_stats()
        duration = time.time() - self.start_time
        
        # 计算差值
        report = {
            'duration_seconds': duration,
            'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
            'end_time': datetime.now().isoformat(),
            'metrics': {}
        }
        
        # 关键性能指标
        key_metrics = [
            'rabbitmq_messages_received_total',
            'rabbitmq_messages_processed_total',
            'rabbitmq_messages_failed_total',
            'database_operations_total',
            'database_pool_size',
            'system_cpu_usage_percent',
            'system_memory_usage_percent'
        ]
        
        for metric in key_metrics:
            baseline = self.baseline_stats.get(metric, 0)
            current = current_stats.get(metric, 0)
            delta = current - baseline
            
            report['metrics'][metric] = {
                'baseline': baseline,
                'current': current,
                'delta': delta,
                'rate_per_second': delta / duration if duration > 0 else 0
            }
        
        return report


class PerformanceTester:
    """性能测试器"""
    
    def __init__(self):
        """初始化性能测试器"""
        self.results = {}
        
    def test_single_threaded_consumer(self, duration: int = 60) -> Dict[str, Any]:
        """测试单线程消费者"""
        print(f"🔧 测试单线程消费者 ({duration}秒)...")
        
        # 启动单线程消费者的监控
        monitor = PerformanceMonitor("http://localhost:8000/metrics")
        
        print("请手动启动单线程消费者:")
        print("python consumer/main.py")
        print("等待消费者启动...")
        
        # 等待用户确认
        input("消费者启动后按回车继续...")
        
        # 开始监控
        monitor.start_monitoring()
        print(f"开始监控，持续 {duration} 秒...")
        
        # 等待测试时间
        time.sleep(duration)
        
        # 获取报告
        report = monitor.get_performance_report()
        report['consumer_type'] = 'single_threaded'
        
        print("✅ 单线程消费者测试完成")
        return report
    
    def test_multi_threaded_consumer(self, duration: int = 60) -> Dict[str, Any]:
        """测试多线程消费者"""
        print(f"🔧 测试多线程消费者 ({duration}秒)...")
        
        # 启动多线程消费者的监控
        monitor = PerformanceMonitor("http://localhost:8001/metrics")
        
        print("请手动启动多线程消费者:")
        print("python consumer/threaded_main.py")
        print("等待消费者启动...")
        
        # 等待用户确认
        input("消费者启动后按回车继续...")
        
        # 开始监控
        monitor.start_monitoring()
        print(f"开始监控，持续 {duration} 秒...")
        
        # 等待测试时间
        time.sleep(duration)
        
        # 获取报告
        report = monitor.get_performance_report()
        report['consumer_type'] = 'multi_threaded'
        
        print("✅ 多线程消费者测试完成")
        return report
    
    def compare_results(self, single_result: Dict, multi_result: Dict) -> Dict[str, Any]:
        """对比测试结果"""
        print("\n📊 性能对比分析")
        print("=" * 80)
        
        comparison = {
            'single_threaded': single_result,
            'multi_threaded': multi_result,
            'improvements': {}
        }
        
        # 关键指标对比
        key_metrics = [
            'rabbitmq_messages_processed_total',
            'database_operations_total'
        ]
        
        print(f"{'指标':<40} {'单线程':<15} {'多线程':<15} {'提升倍数':<10}")
        print("-" * 80)
        
        for metric in key_metrics:
            single_rate = single_result.get('metrics', {}).get(metric, {}).get('rate_per_second', 0)
            multi_rate = multi_result.get('metrics', {}).get(metric, {}).get('rate_per_second', 0)
            
            improvement = multi_rate / single_rate if single_rate > 0 else 0
            comparison['improvements'][metric] = improvement
            
            print(f"{metric:<40} {single_rate:<15.2f} {multi_rate:<15.2f} {improvement:<10.2f}x")
        
        # 计算总体性能提升
        msg_improvement = comparison['improvements'].get('rabbitmq_messages_processed_total', 0)
        db_improvement = comparison['improvements'].get('database_operations_total', 0)
        
        overall_improvement = (msg_improvement + db_improvement) / 2
        comparison['overall_improvement'] = overall_improvement
        
        print("-" * 80)
        print(f"{'总体性能提升':<40} {'':<15} {'':<15} {overall_improvement:<10.2f}x")
        
        # 资源使用对比
        print(f"\n📈 资源使用对比")
        print("-" * 50)
        
        resource_metrics = ['system_cpu_usage_percent', 'system_memory_usage_percent']
        for metric in resource_metrics:
            single_avg = single_result.get('metrics', {}).get(metric, {}).get('current', 0)
            multi_avg = multi_result.get('metrics', {}).get(metric, {}).get('current', 0)
            
            print(f"{metric:<30} 单线程: {single_avg:.1f}% | 多线程: {multi_avg:.1f}%")
        
        return comparison
    
    def run_full_test(self, duration: int = 60) -> Dict[str, Any]:
        """运行完整的性能测试"""
        print("🚀 开始性能测试")
        print("=" * 80)
        print("注意: 请确保有足够的测试数据在RabbitMQ队列中")
        print("可以使用producer发送测试数据")
        print()
        
        # 测试单线程消费者
        single_result = self.test_single_threaded_consumer(duration)
        
        print("\n请停止单线程消费者，然后启动多线程消费者")
        input("准备好后按回车继续...")
        
        # 测试多线程消费者
        multi_result = self.test_multi_threaded_consumer(duration)
        
        # 对比结果
        comparison = self.compare_results(single_result, multi_result)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"performance_test_result_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(comparison, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 测试结果已保存到: {result_file}")
        
        return comparison


def generate_test_data(count: int = 1000):
    """生成测试数据"""
    print(f"🔧 生成 {count} 条测试数据...")
    
    try:
        # 这里可以调用producer来生成测试数据
        # 或者直接向RabbitMQ发送消息
        print("请使用producer发送测试数据:")
        print(f"python producer/main.py --count {count}")
        
    except Exception as e:
        print(f"生成测试数据失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='消费者性能测试工具')
    parser.add_argument('--duration', type=int, default=60, help='测试持续时间（秒）')
    parser.add_argument('--generate-data', type=int, help='生成测试数据数量')
    parser.add_argument('--test-type', choices=['single', 'multi', 'compare'], 
                       default='compare', help='测试类型')
    
    args = parser.parse_args()
    
    tester = PerformanceTester()
    
    try:
        if args.generate_data:
            generate_test_data(args.generate_data)
            return
        
        if args.test_type == 'single':
            result = tester.test_single_threaded_consumer(args.duration)
            print(json.dumps(result, indent=2, default=str))
        elif args.test_type == 'multi':
            result = tester.test_multi_threaded_consumer(args.duration)
            print(json.dumps(result, indent=2, default=str))
        else:  # compare
            result = tester.run_full_test(args.duration)
            
            # 显示最终结论
            improvement = result.get('overall_improvement', 0)
            print(f"\n🎉 测试结论:")
            if improvement >= 10:
                print(f"✅ 多线程消费者性能提升 {improvement:.1f}x，达到10倍性能目标！")
            elif improvement >= 5:
                print(f"✅ 多线程消费者性能提升 {improvement:.1f}x，显著提升！")
            elif improvement >= 2:
                print(f"✅ 多线程消费者性能提升 {improvement:.1f}x，有明显改善")
            else:
                print(f"⚠️  多线程消费者性能提升 {improvement:.1f}x，提升有限")
                print("建议检查:")
                print("- 数据库连接池配置")
                print("- 线程池大小设置")
                print("- 批处理大小调优")
                print("- 网络和I/O瓶颈")
    
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == '__main__':
    main()
