@echo off
REM 生产环境启动脚本
echo ========================================
echo 🌍 启动生产环境服务
echo ========================================

REM 设置环境变量
set ENVIRONMENT=prod

echo 📁 环境: %ENVIRONMENT%
echo 📁 配置目录: config/%ENVIRONMENT%/
echo.

REM 检查环境变量
echo 🔍 检查环境变量...
if "%MYSQL_PASSWORD%"=="" (
    echo ❌ 环境变量 MYSQL_PASSWORD 未设置
    echo 请设置: set MYSQL_PASSWORD=your_password
    pause
    exit /b 1
)

if "%RABBITMQ_PASSWORD%"=="" (
    echo ❌ 环境变量 RABBITMQ_PASSWORD 未设置
    echo 请设置: set RABBITMQ_PASSWORD=your_password
    pause
    exit /b 1
)

if "%INSTANCE_ID%"=="" (
    echo ⚠️  环境变量 INSTANCE_ID 未设置，使用默认值
    set INSTANCE_ID=prod-001
)

if "%DEPLOYMENT_ID%"=="" (
    echo ⚠️  环境变量 DEPLOYMENT_ID 未设置，使用默认值
    set DEPLOYMENT_ID=deploy-001
)

echo ✅ 环境变量检查完成

REM 检查conda环境
echo 🔍 检查conda环境...
call conda info --envs | findstr qmt >nul
if %errorlevel% neq 0 (
    echo ❌ conda环境 'qmt' 不存在，请先创建环境
    echo 运行: conda create -n qmt python=3.10
    pause
    exit /b 1
)

REM 激活conda环境
echo 🔧 激活conda环境 'qmt'...
call conda activate qmt

REM 检查配置文件
echo 🔍 检查配置文件...
if not exist "config\%ENVIRONMENT%\database.yaml" (
    echo ❌ 数据库配置文件不存在: config\%ENVIRONMENT%\database.yaml
    pause
    exit /b 1
)

if not exist "config\%ENVIRONMENT%\rabbitmq_config.yaml" (
    echo ❌ RabbitMQ配置文件不存在: config\%ENVIRONMENT%\rabbitmq_config.yaml
    pause
    exit /b 1
)

if not exist "config\%ENVIRONMENT%\stocks_config.yaml" (
    echo ❌ 股票配置文件不存在: config\%ENVIRONMENT%\stocks_config.yaml
    pause
    exit /b 1
)

echo ✅ 配置文件检查完成

REM 检查依赖
echo 🔍 检查Python依赖...
python -c "import pymysql, pika, yaml, prometheus_client" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python依赖缺失，请安装依赖
    echo 运行: pip install -r requirements.txt
    pause
    exit /b 1
)

echo ✅ 依赖检查完成

REM 显示菜单
echo.
echo 请选择要启动的服务:
echo 1. Producer (生产者)
echo 2. Consumer (单线程消费者)
echo 3. Consumer (多线程消费者) [推荐]
echo 4. 全部服务
echo 5. 退出
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto start_producer
if "%choice%"=="2" goto start_consumer
if "%choice%"=="3" goto start_threaded_consumer
if "%choice%"=="4" goto start_all
if "%choice%"=="5" goto exit
echo ❌ 无效选择
pause
exit /b 1

:start_producer
echo.
echo 🚀 启动Producer (生产环境)...
cd producer
python main.py --env prod --log-level INFO
goto end

:start_consumer
echo.
echo 🚀 启动Consumer (生产环境)...
cd consumer
python main.py --env prod --log-level INFO
goto end

:start_threaded_consumer
echo.
echo 🚀 启动多线程Consumer (生产环境)...
cd consumer
python threaded_main.py --env prod --log-level INFO
goto end

:start_all
echo.
echo 🚀 启动所有服务 (生产环境)...
echo 注意: 这将在多个窗口中启动服务
echo.

REM 启动Producer
start "Producer-Prod" cmd /k "cd producer && python main.py --env prod --log-level INFO"
timeout /t 3 /nobreak >nul

REM 启动多线程Consumer (生产环境推荐)
start "Consumer-Prod" cmd /k "cd consumer && python threaded_main.py --env prod --log-level INFO"
timeout /t 3 /nobreak >nul

echo ✅ 所有服务已启动
echo 📊 监控端点:
echo   Producer健康检查: http://localhost:8081/health
echo   Producer指标: http://localhost:8001/metrics
echo   Consumer健康检查: http://localhost:9081/health
echo   Consumer指标: http://localhost:9091/metrics
echo.
echo 🔧 生产环境注意事项:
echo   - 确保数据库服务正常运行
echo   - 确保RabbitMQ集群正常运行
echo   - 监控系统资源使用情况
echo   - 定期检查日志文件
goto end

:exit
echo 👋 退出
goto end

:end
echo.
echo 按任意键退出...
pause >nul
