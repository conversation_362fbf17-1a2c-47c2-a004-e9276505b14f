#!/usr/bin/env python3
"""
ClickHouse功能测试脚本
用于测试ClickHouse数据库管理器的基本功能
"""

import sys
import os
import logging
import argparse
from datetime import datetime
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from consumer.clickhouse_manager import ClickHouseManager
    from consumer.database_factory import DatabaseFactory, DualDatabaseManager
    CLICKHOUSE_AVAILABLE = True
except ImportError as e:
    print(f"❌ ClickHouse依赖不可用: {e}")
    print("请安装依赖: pip install clickhouse-driver")
    sys.exit(1)


class ClickHouseTester:
    """ClickHouse测试器"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """初始化测试器"""
        self.config_path = config_path
        self.logger = self._setup_logger()
        self.manager = None
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            self.logger.info("🔗 测试ClickHouse连接...")
            self.manager = ClickHouseManager(self.config_path)
            
            # 测试健康检查
            health = self.manager.health_check()
            
            if health['status'] == 'healthy':
                self.logger.info("✅ ClickHouse连接成功")
                self.logger.info(f"   数据库类型: {health['database_type']}")
                self.logger.info(f"   连接池大小: {health.get('pool_size', 'N/A')}")
                return True
            else:
                self.logger.error(f"❌ ClickHouse连接失败: {health.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 连接测试失败: {e}")
            return False
    
    def test_insert_single_quote(self) -> bool:
        """测试插入单条行情数据"""
        try:
            self.logger.info("📊 测试插入单条行情数据...")
            
            symbol = "TEST001.SH"
            quote_data = {
                'time': int(time.time() * 1000),  # 当前时间戳（毫秒）
                'timetag': datetime.now().strftime('%Y%m%d %H:%M:%S'),
                'lastPrice': 10.5,
                'open': 10.0,
                'high': 10.8,
                'low': 9.8,
                'lastClose': 10.2,
                'amount': 1000000,
                'volume': 50000,
                'pvolume': 500000,
                'stockStatus': 1,
                'openInt': 0,
                'settlementPrice': None,
                'lastSettlementPrice': None
            }
            
            result = self.manager.insert_market_quote(symbol, quote_data)
            
            if result:
                self.logger.info(f"✅ 成功插入行情数据: {symbol}")
                return True
            else:
                self.logger.error(f"❌ 插入行情数据失败: {symbol}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 插入测试失败: {e}")
            return False
    
    def test_insert_with_depth(self) -> bool:
        """测试插入带盘口数据的行情"""
        try:
            self.logger.info("📈 测试插入带盘口数据的行情...")
            
            symbol = "TEST002.SH"
            quote_data = {
                'time': int(time.time() * 1000),
                'timetag': datetime.now().strftime('%Y%m%d %H:%M:%S'),
                'lastPrice': 15.5,
                'open': 15.0,
                'high': 15.8,
                'low': 14.8,
                'lastClose': 15.2,
                'amount': 2000000,
                'volume': 80000,
                'pvolume': 800000,
                'stockStatus': 1,
                'openInt': 0,
                # 盘口数据
                'askPrice': [15.6, 15.7, 15.8, 15.9, 16.0],
                'bidPrice': [15.5, 15.4, 15.3, 15.2, 15.1],
                'askVol': [100, 200, 300, 400, 500],
                'bidVol': [150, 250, 350, 450, 550]
            }
            
            result = self.manager.insert_market_quote(symbol, quote_data)
            
            if result:
                self.logger.info(f"✅ 成功插入带盘口数据的行情: {symbol}")
                return True
            else:
                self.logger.error(f"❌ 插入带盘口数据的行情失败: {symbol}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 盘口数据插入测试失败: {e}")
            return False
    
    def test_batch_insert(self) -> bool:
        """测试批量插入"""
        try:
            self.logger.info("📦 测试批量插入...")
            
            quotes = []
            for i in range(10):
                quotes.append({
                    'symbol': f'BATCH{i:03d}.SH',
                    'quote_data': {
                        'time': int(time.time() * 1000) + i * 1000,
                        'timetag': datetime.now().strftime('%Y%m%d %H:%M:%S'),
                        'lastPrice': 20.0 + i * 0.1,
                        'open': 19.5 + i * 0.1,
                        'high': 20.5 + i * 0.1,
                        'low': 19.0 + i * 0.1,
                        'volume': 1000 * (i + 1),
                        'amount': 20000 * (i + 1)
                    }
                })
            
            result = self.manager.batch_insert_market_quotes(quotes)
            
            if result:
                self.logger.info(f"✅ 成功批量插入 {len(quotes)} 条记录")
                return True
            else:
                self.logger.error("❌ 批量插入失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 批量插入测试失败: {e}")
            return False
    
    def test_query_latest_quotes(self) -> bool:
        """测试查询最新行情"""
        try:
            self.logger.info("🔍 测试查询最新行情...")
            
            # 查询指定股票的最新行情
            symbols = ["TEST001.SH", "TEST002.SH"]
            quotes = self.manager.get_latest_quotes(symbols)
            
            if quotes and len(quotes) > 0:
                self.logger.info(f"✅ 成功查询到 {len(quotes)} 条最新行情")
                for quote in quotes:
                    symbol = quote.get('symbol', 'N/A')
                    price = quote.get('last_price', 'N/A')
                    self.logger.info(f"   {symbol}: 最新价 = {price}")
                return True
            else:
                self.logger.warning("⚠️  未查询到行情数据（可能是正常的）")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 查询测试失败: {e}")
            return False
    
    def test_log_processing_result(self) -> bool:
        """测试记录处理结果"""
        try:
            self.logger.info("📝 测试记录处理结果...")
            
            result = self.manager.log_processing_result(
                message_id="test-message-" + str(int(time.time())),
                symbol="TEST001.SH",
                status="SUCCESS",
                error_message=None,
                raw_data={"test": "data", "timestamp": time.time()}
            )
            
            if result:
                self.logger.info("✅ 成功记录处理结果")
                return True
            else:
                self.logger.error("❌ 记录处理结果失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 日志记录测试失败: {e}")
            return False
    
    def test_database_factory(self) -> bool:
        """测试数据库工厂"""
        try:
            self.logger.info("🏭 测试数据库工厂...")
            
            # 测试创建ClickHouse管理器
            manager = DatabaseFactory.create_database_manager(self.config_path)
            
            if manager and manager.get_database_type() == 'clickhouse':
                self.logger.info("✅ 数据库工厂测试成功")
                return True
            else:
                self.logger.error("❌ 数据库工厂测试失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 数据库工厂测试失败: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        self.logger.info("🚀 开始ClickHouse功能测试...")
        self.logger.info("=" * 60)
        
        tests = [
            ("连接测试", self.test_connection),
            ("单条插入测试", self.test_insert_single_quote),
            ("盘口数据插入测试", self.test_insert_with_depth),
            ("批量插入测试", self.test_batch_insert),
            ("查询测试", self.test_query_latest_quotes),
            ("日志记录测试", self.test_log_processing_result),
            ("数据库工厂测试", self.test_database_factory)
        ]
        
        success_count = 0
        total_count = len(tests)
        
        for test_name, test_func in tests:
            self.logger.info(f"🔧 执行 {test_name}...")
            try:
                if test_func():
                    success_count += 1
                    self.logger.info(f"✅ {test_name} 通过")
                else:
                    self.logger.error(f"❌ {test_name} 失败")
            except Exception as e:
                self.logger.error(f"❌ {test_name} 异常: {e}")
            
            self.logger.info("-" * 40)
        
        # 清理测试数据
        if self.manager:
            try:
                self.manager.close()
            except:
                pass
        
        self.logger.info("=" * 60)
        self.logger.info("📋 测试结果汇总:")
        self.logger.info(f"   总测试数: {total_count}")
        self.logger.info(f"   成功数: {success_count}")
        self.logger.info(f"   失败数: {total_count - success_count}")
        self.logger.info(f"   成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            self.logger.info("🎉 所有测试通过！ClickHouse功能正常")
            return True
        else:
            self.logger.warning("⚠️  部分测试失败，请检查ClickHouse配置和服务状态")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ClickHouse功能测试工具')
    parser.add_argument('--config', default='config/database.yaml', help='数据库配置文件路径')
    parser.add_argument('--test', choices=['connection', 'insert', 'query', 'batch', 'all'], 
                       default='all', help='要运行的测试类型')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = ClickHouseTester(args.config)
    
    try:
        if args.test == 'connection':
            success = tester.test_connection()
        elif args.test == 'insert':
            success = tester.test_connection() and tester.test_insert_single_quote()
        elif args.test == 'query':
            success = tester.test_connection() and tester.test_query_latest_quotes()
        elif args.test == 'batch':
            success = tester.test_connection() and tester.test_batch_insert()
        else:  # all
            success = tester.run_all_tests()
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"测试失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
