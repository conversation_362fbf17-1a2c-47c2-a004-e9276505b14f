@echo off
REM 开发环境启动脚本
echo ========================================
echo 🌍 启动开发环境服务
echo ========================================

REM 设置环境变量
set ENVIRONMENT=dev
set MYSQL_PASSWORD=123456
set RABBITMQ_PASSWORD=admin123

echo 📁 环境: %ENVIRONMENT%
echo 📁 配置目录: config/%ENVIRONMENT%/
echo.

REM 检查conda环境
echo 🔍 检查conda环境...
call conda info --envs | findstr qmt >nul
if %errorlevel% neq 0 (
    echo ❌ conda环境 'qmt' 不存在，请先创建环境
    echo 运行: conda create -n qmt python=3.10
    pause
    exit /b 1
)

REM 激活conda环境
echo 🔧 激活conda环境 'qmt'...
call conda activate qmt

REM 检查配置文件
echo 🔍 检查配置文件...
if not exist "config\%ENVIRONMENT%\database.yaml" (
    echo ❌ 数据库配置文件不存在: config\%ENVIRONMENT%\database.yaml
    pause
    exit /b 1
)

if not exist "config\%ENVIRONMENT%\rabbitmq_config.yaml" (
    echo ❌ RabbitMQ配置文件不存在: config\%ENVIRONMENT%\rabbitmq_config.yaml
    pause
    exit /b 1
)

if not exist "config\%ENVIRONMENT%\stocks_config.yaml" (
    echo ❌ 股票配置文件不存在: config\%ENVIRONMENT%\stocks_config.yaml
    pause
    exit /b 1
)

echo ✅ 配置文件检查完成

REM 显示菜单
echo.
echo 请选择要启动的服务:
echo 1. Producer (生产者)
echo 2. Consumer (单线程消费者)
echo 3. Consumer (多线程消费者)
echo 4. 全部服务
echo 5. 退出
echo.

set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" goto start_producer
if "%choice%"=="2" goto start_consumer
if "%choice%"=="3" goto start_threaded_consumer
if "%choice%"=="4" goto start_all
if "%choice%"=="5" goto exit
echo ❌ 无效选择
pause
exit /b 1

:start_producer
echo.
echo 🚀 启动Producer (开发环境)...
cd producer
python main.py --env dev --log-level DEBUG
goto end

:start_consumer
echo.
echo 🚀 启动Consumer (开发环境)...
cd consumer
python main.py --env dev --log-level DEBUG
goto end

:start_threaded_consumer
echo.
echo 🚀 启动多线程Consumer (开发环境)...
cd consumer
python threaded_main.py --env dev --log-level DEBUG
goto end

:start_all
echo.
echo 🚀 启动所有服务 (开发环境)...
echo 注意: 这将在多个窗口中启动服务
echo.

REM 启动Producer
start "Producer-Dev" cmd /k "cd producer && python main.py --env dev --log-level DEBUG"
timeout /t 2 /nobreak >nul

REM 启动Consumer
start "Consumer-Dev" cmd /k "cd consumer && python main.py --env dev --log-level DEBUG"
timeout /t 2 /nobreak >nul

echo ✅ 所有服务已启动
echo 📊 监控端点:
echo   Producer健康检查: http://localhost:8081/health
echo   Producer指标: http://localhost:8001/metrics
echo   Consumer健康检查: http://localhost:8080/health
echo   Consumer指标: http://localhost:8000/metrics
goto end

:exit
echo 👋 退出
goto end

:end
echo.
echo 按任意键退出...
pause >nul
