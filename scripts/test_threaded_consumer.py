#!/usr/bin/env python3
"""
多线程消费者测试脚本
验证修复后的多线程消费者是否正常工作
"""

import sys
import os
import time
import json
import requests
import threading
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class ThreadedConsumerTester:
    """多线程消费者测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.health_url = "http://localhost:8081/health"
        self.metrics_url = "http://localhost:8001/metrics"
        
    def check_consumer_health(self) -> bool:
        """检查消费者健康状态"""
        try:
            response = requests.get(self.health_url, timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ 消费者健康状态: {health_data.get('status', 'unknown')}")
                
                # 检查各组件状态
                components = ['rabbitmq', 'database', 'consumer', 'batch_processor', 'ack_processor']
                for component in components:
                    status = health_data.get(component, 'unknown')
                    if isinstance(status, dict):
                        status = status.get('status', 'unknown')
                    print(f"   {component}: {status}")
                
                return health_data.get('status') == 'healthy'
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return False
    
    def get_consumer_metrics(self) -> dict:
        """获取消费者指标"""
        try:
            response = requests.get(self.metrics_url, timeout=5)
            if response.status_code == 200:
                metrics = {}
                for line in response.text.split('\n'):
                    if line.startswith('#') or not line.strip():
                        continue
                    
                    parts = line.split(' ')
                    if len(parts) >= 2:
                        metric_name = parts[0]
                        try:
                            metric_value = float(parts[1])
                            metrics[metric_name] = metric_value
                        except ValueError:
                            continue
                
                return metrics
            else:
                print(f"❌ 获取指标失败: HTTP {response.status_code}")
                return {}
        except Exception as e:
            print(f"❌ 获取指标失败: {e}")
            return {}
    
    def monitor_performance(self, duration: int = 30):
        """监控性能"""
        print(f"📊 开始监控性能 ({duration}秒)...")
        
        start_metrics = self.get_consumer_metrics()
        start_time = time.time()
        
        # 监控期间
        time.sleep(duration)
        
        end_metrics = self.get_consumer_metrics()
        end_time = time.time()
        
        # 计算性能指标
        duration_actual = end_time - start_time
        
        # 关键指标
        key_metrics = [
            'messages_received_total',
            'messages_processed_total',
            'messages_failed_total',
            'consumer_batches_processed_total',
            'consumer_records_processed_total'
        ]
        
        print(f"\n📈 性能报告 ({duration_actual:.1f}秒):")
        print("-" * 60)
        
        for metric in key_metrics:
            start_value = start_metrics.get(metric, 0)
            end_value = end_metrics.get(metric, 0)
            delta = end_value - start_value
            rate = delta / duration_actual if duration_actual > 0 else 0
            
            print(f"{metric:<35} {delta:>8.0f} ({rate:>6.1f}/s)")
        
        # 队列深度和线程状态
        print(f"\n🔧 系统状态:")
        print(f"队列深度: {end_metrics.get('consumer_queue_depth', 0)}")
        print(f"线程池大小: {end_metrics.get('consumer_thread_pool_size', 0)}")
        print(f"批处理大小: {end_metrics.get('consumer_batch_size', 0)}")
        print(f"平均批处理时间: {end_metrics.get('consumer_avg_batch_processing_time_seconds', 0):.3f}s")
        
        return {
            'duration': duration_actual,
            'start_metrics': start_metrics,
            'end_metrics': end_metrics,
            'rates': {metric: (end_metrics.get(metric, 0) - start_metrics.get(metric, 0)) / duration_actual 
                     for metric in key_metrics}
        }
    
    def test_delivery_tag_handling(self):
        """测试delivery tag处理"""
        print("🔍 测试delivery tag处理...")
        
        # 获取初始指标
        initial_metrics = self.get_consumer_metrics()
        initial_failed = initial_metrics.get('messages_failed_total', 0)
        
        # 监控一段时间
        time.sleep(10)
        
        # 获取最终指标
        final_metrics = self.get_consumer_metrics()
        final_failed = final_metrics.get('messages_failed_total', 0)
        
        failed_increase = final_failed - initial_failed
        
        if failed_increase == 0:
            print("✅ 没有检测到delivery tag错误")
            return True
        else:
            print(f"⚠️  检测到 {failed_increase} 个失败消息，可能存在delivery tag问题")
            return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 开始多线程消费者综合测试")
        print("=" * 60)
        
        # 1. 健康检查
        print("1. 检查消费者健康状态...")
        health_ok = self.check_consumer_health()
        
        if not health_ok:
            print("❌ 消费者不健康，测试终止")
            return False
        
        print()
        
        # 2. 测试delivery tag处理
        print("2. 测试delivery tag处理...")
        delivery_tag_ok = self.test_delivery_tag_handling()
        print()
        
        # 3. 性能监控
        print("3. 性能监控...")
        performance_report = self.monitor_performance(30)
        print()
        
        # 4. 最终健康检查
        print("4. 最终健康检查...")
        final_health_ok = self.check_consumer_health()
        print()
        
        # 总结
        print("📋 测试结果总结:")
        print("-" * 40)
        print(f"健康状态: {'✅' if health_ok and final_health_ok else '❌'}")
        print(f"Delivery Tag处理: {'✅' if delivery_tag_ok else '❌'}")
        
        # 性能评估
        rates = performance_report['rates']
        msg_rate = rates.get('messages_processed_total', 0)
        batch_rate = rates.get('consumer_batches_processed_total', 0)
        
        print(f"消息处理速率: {msg_rate:.1f} msg/s")
        print(f"批处理速率: {batch_rate:.1f} batch/s")
        
        if msg_rate > 50:
            print("✅ 性能表现良好")
            performance_ok = True
        elif msg_rate > 10:
            print("⚠️  性能一般")
            performance_ok = True
        else:
            print("❌ 性能较差")
            performance_ok = False
        
        # 总体评估
        overall_ok = health_ok and final_health_ok and delivery_tag_ok and performance_ok
        
        print(f"\n🎯 总体评估: {'✅ 测试通过' if overall_ok else '❌ 测试失败'}")
        
        if overall_ok:
            print("🎉 多线程消费者工作正常，delivery tag问题已修复！")
        else:
            print("⚠️  多线程消费者存在问题，需要进一步调试")
        
        return overall_ok


def main():
    """主函数"""
    print("🔧 多线程消费者测试工具")
    print("=" * 60)
    print("请确保多线程消费者正在运行:")
    print("python consumer/threaded_main.py")
    print()
    
    # 等待用户确认
    try:
        input("消费者启动后按回车开始测试...")
    except KeyboardInterrupt:
        print("\n测试被用户取消")
        return
    
    # 运行测试
    tester = ThreadedConsumerTester()
    
    try:
        success = tester.run_comprehensive_test()
        
        if success:
            print("\n🎉 所有测试通过！多线程消费者工作正常")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查日志")
            return 1
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
