#!/usr/bin/env python3
"""
ClickHouse初始化脚本
用于自动创建ClickHouse数据库表和相关配置
"""

import sys
import os
import logging
import argparse
from typing import Dict, Any, List
import yaml

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from clickhouse_driver import Client
    from clickhouse_driver.errors import Error as ClickHouseError
except ImportError:
    print("❌ 请先安装ClickHouse驱动: pip install clickhouse-driver")
    sys.exit(1)


class ClickHouseInitializer:
    """ClickHouse初始化器"""
    
    def __init__(self, config_path: str = "../config/database.yaml"):
        """初始化"""
        self.config_path = config_path
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        self.client = None
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"数据库配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def connect(self) -> bool:
        """连接到ClickHouse"""
        try:
            ch_config = self.config['clickhouse']['primary']
            
            self.client = Client(
                host=ch_config['host'],
                port=ch_config['port'],
                user=ch_config['username'],
                password=ch_config['password'],
                connect_timeout=ch_config.get('connect_timeout', 10)
            )
            
            # 测试连接
            result = self.client.execute("SELECT version()")
            version = result[0][0] if result else "Unknown"
            
            self.logger.info(f"✅ 成功连接到ClickHouse服务器")
            self.logger.info(f"   服务器版本: {version}")
            self.logger.info(f"   主机: {ch_config['host']}:{ch_config['port']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 连接ClickHouse失败: {e}")
            return False
    
    def check_server_status(self) -> bool:
        """检查服务器状态"""
        try:
            if not self.client:
                return False
            
            # 检查服务器状态
            result = self.client.execute("SELECT 1")
            if result and result[0][0] == 1:
                self.logger.info("✅ ClickHouse服务器状态正常")
                return True
            else:
                self.logger.error("❌ ClickHouse服务器状态异常")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 检查服务器状态失败: {e}")
            return False
    
    def create_database(self) -> bool:
        """创建数据库"""
        try:
            database_name = self.config['clickhouse']['primary']['database']
            
            # 检查数据库是否存在
            result = self.client.execute(f"EXISTS DATABASE {database_name}")
            if result and result[0][0] == 1:
                self.logger.info(f"📊 数据库 '{database_name}' 已存在")
                return True
            
            # 创建数据库
            self.client.execute(f"CREATE DATABASE {database_name}")
            self.logger.info(f"✅ 成功创建数据库: {database_name}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 创建数据库失败: {e}")
            return False
    
    def execute_schema_file(self, schema_file: str = "../config/clickhouse_schema.sql") -> bool:
        """执行表结构SQL文件"""
        try:
            if not os.path.exists(schema_file):
                self.logger.error(f"❌ 表结构文件不存在: {schema_file}")
                return False
            
            self.logger.info(f"📄 开始执行表结构文件: {schema_file}")
            
            with open(schema_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句
            statements = self._split_sql_statements(sql_content)
            
            success_count = 0
            total_count = len(statements)
            
            for i, statement in enumerate(statements, 1):
                statement = statement.strip()
                if not statement or statement.startswith('--'):
                    continue
                
                try:
                    self.client.execute(statement)
                    success_count += 1
                    self.logger.debug(f"✅ 执行成功 ({i}/{total_count}): {statement[:50]}...")
                    
                except Exception as e:
                    # 某些语句可能因为已存在而失败，这是正常的
                    if "already exists" in str(e).lower() or "duplicate" in str(e).lower():
                        success_count += 1
                        self.logger.debug(f"⚠️  已存在 ({i}/{total_count}): {statement[:50]}...")
                    else:
                        self.logger.error(f"❌ 执行失败 ({i}/{total_count}): {e}")
                        self.logger.error(f"   SQL: {statement[:100]}...")
            
            self.logger.info(f"📊 表结构执行完成: {success_count}/{total_count} 成功")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"❌ 执行表结构文件失败: {e}")
            return False
    
    def _split_sql_statements(self, sql_content: str) -> List[str]:
        """分割SQL语句"""
        # 简单的SQL语句分割，按分号分割
        statements = []
        current_statement = ""
        in_string = False
        escape_next = False
        
        for char in sql_content:
            if escape_next:
                current_statement += char
                escape_next = False
                continue
            
            if char == '\\':
                escape_next = True
                current_statement += char
                continue
            
            if char in ("'", '"'):
                in_string = not in_string
                current_statement += char
                continue
            
            if char == ';' and not in_string:
                if current_statement.strip():
                    statements.append(current_statement.strip())
                current_statement = ""
                continue
            
            current_statement += char
        
        # 添加最后一个语句
        if current_statement.strip():
            statements.append(current_statement.strip())
        
        return statements
    
    def check_tables(self) -> Dict[str, bool]:
        """检查表是否创建成功"""
        try:
            database_name = self.config['clickhouse']['primary']['database']
            
            # 获取所有表
            result = self.client.execute(f"""
                SELECT name, engine
                FROM system.tables
                WHERE database = '{database_name}'
                ORDER BY name
            """)
            
            existing_tables = {row[0]: row[1] for row in result}
            
            # 期望的表
            expected_tables = [
                'stock_info',
                'market_quotes',
                'market_depth',
                'processing_log',
                'system_metrics',
                'market_quotes_1m',
                'market_quotes_1h'
            ]
            
            table_status = {}
            
            self.logger.info("📊 表创建状态检查:")
            for table in expected_tables:
                if table in existing_tables:
                    engine = existing_tables[table]
                    self.logger.info(f"   ✅ {table} ({engine})")
                    table_status[table] = True
                else:
                    self.logger.warning(f"   ❌ {table} (不存在)")
                    table_status[table] = False
            
            # 显示额外的表
            extra_tables = set(existing_tables.keys()) - set(expected_tables)
            if extra_tables:
                self.logger.info("📋 其他表:")
                for table in sorted(extra_tables):
                    engine = existing_tables[table]
                    self.logger.info(f"   📄 {table} ({engine})")
            
            return table_status
            
        except Exception as e:
            self.logger.error(f"❌ 检查表状态失败: {e}")
            return {}
    
    def check_views_and_dictionaries(self) -> Dict[str, bool]:
        """检查视图和字典"""
        try:
            database_name = self.config['clickhouse']['primary']['database']
            
            # 检查物化视图
            mv_result = self.client.execute(f"""
                SELECT name
                FROM system.tables
                WHERE database = '{database_name}'
                AND engine LIKE '%MaterializedView%'
                ORDER BY name
            """)
            
            existing_mvs = [row[0] for row in mv_result]
            expected_mvs = ['latest_quotes_mv', 'market_quotes_1m_mv', 'market_quotes_1h_mv']
            
            # 检查字典
            dict_result = self.client.execute(f"""
                SELECT name, status
                FROM system.dictionaries
                WHERE database = '{database_name}'
                ORDER BY name
            """)
            
            existing_dicts = {row[0]: row[1] for row in dict_result}
            expected_dicts = ['stock_info_dict']
            
            self.logger.info("📊 物化视图状态:")
            mv_status = {}
            for mv in expected_mvs:
                if mv in existing_mvs:
                    self.logger.info(f"   ✅ {mv}")
                    mv_status[mv] = True
                else:
                    self.logger.warning(f"   ❌ {mv} (不存在)")
                    mv_status[mv] = False
            
            self.logger.info("📊 字典状态:")
            dict_status = {}
            for dict_name in expected_dicts:
                if dict_name in existing_dicts:
                    status = existing_dicts[dict_name]
                    self.logger.info(f"   ✅ {dict_name} ({status})")
                    dict_status[dict_name] = True
                else:
                    self.logger.warning(f"   ❌ {dict_name} (不存在)")
                    dict_status[dict_name] = False
            
            return {**mv_status, **dict_status}
            
        except Exception as e:
            self.logger.error(f"❌ 检查视图和字典失败: {e}")
            return {}
    
    def test_insert_and_query(self) -> bool:
        """测试插入和查询"""
        try:
            database_name = self.config['clickhouse']['primary']['database']
            
            # 切换到目标数据库
            self.client.execute(f"USE {database_name}")
            
            # 测试插入数据
            test_data = [
                ('TEST001.SH', '2025-06-23 10:00:00', 1719115200000, '20250623 10:00:00',
                 10.5, 10.0, 10.8, 9.8, 10.2, 1000000, 50000, 500000, 1, 0, None, None, '2025-06-23 10:00:00')
            ]
            
            self.client.execute("""
                INSERT INTO market_quotes VALUES
            """, test_data)
            
            # 测试查询
            result = self.client.execute("""
                SELECT symbol, last_price, volume
                FROM market_quotes
                WHERE symbol = 'TEST001.SH'
                LIMIT 1
            """)
            
            if result and len(result) > 0:
                symbol, price, volume = result[0]
                self.logger.info(f"✅ 测试插入和查询成功")
                self.logger.info(f"   测试数据: {symbol}, 价格={price}, 成交量={volume}")
                
                # 清理测试数据
                self.client.execute("DELETE FROM market_quotes WHERE symbol = 'TEST001.SH'")
                
                return True
            else:
                self.logger.error("❌ 测试查询失败：未找到插入的数据")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 测试插入和查询失败: {e}")
            return False
    
    def run_full_initialization(self, schema_file: str = "../config/clickhouse_schema.sql") -> bool:
        """运行完整初始化"""
        self.logger.info("🚀 开始ClickHouse初始化...")
        self.logger.info("=" * 60)
        
        steps = [
            ("连接服务器", self.connect),
            ("检查服务器状态", self.check_server_status),
            ("创建数据库", self.create_database),
            ("执行表结构", lambda: self.execute_schema_file(schema_file)),
            ("测试功能", self.test_insert_and_query)
        ]
        
        success_count = 0
        
        for step_name, step_func in steps:
            self.logger.info(f"🔧 {step_name}...")
            try:
                if step_func():
                    success_count += 1
                    self.logger.info(f"✅ {step_name} 完成")
                else:
                    self.logger.error(f"❌ {step_name} 失败")
                    break
            except Exception as e:
                self.logger.error(f"❌ {step_name} 异常: {e}")
                break
        
        # 检查创建结果
        if success_count == len(steps):
            self.logger.info("🔍 检查创建结果...")
            table_status = self.check_tables()
            view_dict_status = self.check_views_and_dictionaries()
            
            all_tables_ok = all(table_status.values())
            all_views_ok = all(view_dict_status.values())
            
            self.logger.info("=" * 60)
            self.logger.info("📋 初始化结果汇总:")
            self.logger.info(f"   基础步骤: {success_count}/{len(steps)} 成功")
            self.logger.info(f"   数据表: {'✅' if all_tables_ok else '⚠️'} ({sum(table_status.values())}/{len(table_status)})")
            self.logger.info(f"   视图字典: {'✅' if all_views_ok else '⚠️'} ({sum(view_dict_status.values())}/{len(view_dict_status)})")
            
            if success_count == len(steps) and all_tables_ok:
                self.logger.info("🎉 ClickHouse初始化完成！")
                return True
            else:
                self.logger.warning("⚠️  初始化部分成功，请检查上述输出")
                return False
        else:
            self.logger.error("❌ 初始化失败")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.client:
            try:
                self.client.disconnect()
            except:
                pass


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ClickHouse初始化工具')
    parser.add_argument('--config', default='../config/database.yaml', help='数据库配置文件路径')
    parser.add_argument('--schema', default='../config/clickhouse_schema.sql', help='表结构SQL文件路径')
    parser.add_argument('--check-only', action='store_true', help='仅检查状态，不执行初始化')
    
    args = parser.parse_args()
    
    # 创建初始化器
    initializer = ClickHouseInitializer(args.config)
    
    try:
        if args.check_only:
            # 仅检查状态
            if initializer.connect():
                initializer.check_tables()
                initializer.check_views_and_dictionaries()
                return 0
            else:
                return 1
        else:
            # 运行完整初始化
            success = initializer.run_full_initialization(args.schema)
            return 0 if success else 1
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 1
    except Exception as e:
        print(f"操作失败: {e}")
        return 1
    finally:
        initializer.disconnect()


if __name__ == '__main__':
    exit(main())
