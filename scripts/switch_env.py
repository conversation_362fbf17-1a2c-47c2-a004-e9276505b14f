#!/usr/bin/env python3
"""
环境切换工具
用于在开发环境和生产环境之间切换配置
"""

import os
import sys
import shutil
import argparse
from pathlib import Path


class EnvironmentSwitcher:
    """环境切换器"""
    
    def __init__(self):
        """初始化环境切换器"""
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / 'config'
        self.dev_dir = self.config_dir / 'dev'
        self.prod_dir = self.config_dir / 'prod'
        
        # 配置文件列表
        self.config_files = [
            'database.yaml',
            'rabbitmq_config.yaml',
            'stocks_config.yaml'
        ]
    
    def check_environment_configs(self):
        """检查环境配置文件"""
        print("🔍 检查环境配置文件...")
        
        missing_files = []
        
        # 检查开发环境配置
        for config_file in self.config_files:
            dev_file = self.dev_dir / config_file
            if not dev_file.exists():
                missing_files.append(f"dev/{config_file}")
        
        # 检查生产环境配置
        for config_file in self.config_files:
            prod_file = self.prod_dir / config_file
            if not prod_file.exists():
                missing_files.append(f"prod/{config_file}")
        
        if missing_files:
            print("❌ 缺失配置文件:")
            for file in missing_files:
                print(f"   - config/{file}")
            return False
        
        print("✅ 所有环境配置文件存在")
        return True
    
    def get_current_environment(self):
        """获取当前环境"""
        # 检查主配置目录中的文件来判断当前环境
        main_db_config = self.config_dir / 'database.yaml'
        
        if not main_db_config.exists():
            return None
        
        # 读取配置文件内容来判断环境
        try:
            with open(main_db_config, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'market_data_dev' in content:
                    return 'dev'
                elif 'market_data_prod' in content:
                    return 'prod'
        except Exception:
            pass
        
        return 'unknown'
    
    def switch_to_environment(self, env: str):
        """切换到指定环境"""
        if env not in ['dev', 'prod']:
            print(f"❌ 无效环境: {env}")
            return False
        
        source_dir = self.dev_dir if env == 'dev' else self.prod_dir
        
        print(f"🔄 切换到 {env.upper()} 环境...")
        
        # 备份当前配置
        backup_dir = self.config_dir / f'backup_{env}'
        if backup_dir.exists():
            shutil.rmtree(backup_dir)
        backup_dir.mkdir(exist_ok=True)
        
        # 复制当前配置到备份目录
        for config_file in self.config_files:
            main_file = self.config_dir / config_file
            if main_file.exists():
                shutil.copy2(main_file, backup_dir / config_file)
        
        # 复制环境配置到主配置目录
        for config_file in self.config_files:
            source_file = source_dir / config_file
            target_file = self.config_dir / config_file
            
            if source_file.exists():
                shutil.copy2(source_file, target_file)
                print(f"   ✅ {config_file}")
            else:
                print(f"   ❌ {config_file} (源文件不存在)")
                return False
        
        print(f"✅ 已切换到 {env.upper()} 环境")
        print(f"📁 备份保存在: {backup_dir}")
        return True
    
    def show_environment_info(self):
        """显示环境信息"""
        current_env = self.get_current_environment()
        
        print("📋 环境信息:")
        print(f"   当前环境: {current_env.upper() if current_env else '未知'}")
        print(f"   配置目录: {self.config_dir}")
        print(f"   开发环境配置: {self.dev_dir}")
        print(f"   生产环境配置: {self.prod_dir}")
        
        print("\n📁 配置文件状态:")
        for config_file in self.config_files:
            main_file = self.config_dir / config_file
            dev_file = self.dev_dir / config_file
            prod_file = self.prod_dir / config_file
            
            print(f"   {config_file}:")
            print(f"     主配置: {'✅' if main_file.exists() else '❌'}")
            print(f"     开发环境: {'✅' if dev_file.exists() else '❌'}")
            print(f"     生产环境: {'✅' if prod_file.exists() else '❌'}")
    
    def validate_environment(self, env: str):
        """验证环境配置"""
        if env not in ['dev', 'prod']:
            print(f"❌ 无效环境: {env}")
            return False
        
        env_dir = self.dev_dir if env == 'dev' else self.prod_dir
        
        print(f"🔍 验证 {env.upper()} 环境配置...")
        
        # 检查配置文件
        for config_file in self.config_files:
            config_path = env_dir / config_file
            if not config_path.exists():
                print(f"   ❌ {config_file} 不存在")
                return False
            
            # 简单的配置文件内容验证
            try:
                import yaml
                with open(config_path, 'r', encoding='utf-8') as f:
                    yaml.safe_load(f)
                print(f"   ✅ {config_file}")
            except Exception as e:
                print(f"   ❌ {config_file} 格式错误: {e}")
                return False
        
        print(f"✅ {env.upper()} 环境配置验证通过")
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='环境切换工具')
    parser.add_argument('action', choices=['switch', 'info', 'validate'], 
                       help='操作类型')
    parser.add_argument('--env', choices=['dev', 'prod'], 
                       help='环境名称 (switch和validate操作需要)')
    
    args = parser.parse_args()
    
    switcher = EnvironmentSwitcher()
    
    # 检查环境配置文件
    if not switcher.check_environment_configs():
        print("\n❌ 请先确保所有环境配置文件存在")
        return 1
    
    if args.action == 'info':
        switcher.show_environment_info()
        return 0
    
    elif args.action == 'validate':
        if not args.env:
            print("❌ validate操作需要指定 --env 参数")
            return 1
        
        if switcher.validate_environment(args.env):
            return 0
        else:
            return 1
    
    elif args.action == 'switch':
        if not args.env:
            print("❌ switch操作需要指定 --env 参数")
            return 1
        
        current_env = switcher.get_current_environment()
        if current_env == args.env:
            print(f"ℹ️  当前已经是 {args.env.upper()} 环境")
            return 0
        
        # 验证目标环境
        if not switcher.validate_environment(args.env):
            print(f"❌ {args.env.upper()} 环境配置验证失败")
            return 1
        
        # 执行切换
        if switcher.switch_to_environment(args.env):
            print(f"\n🎉 成功切换到 {args.env.upper()} 环境")
            print("\n📋 下一步:")
            if args.env == 'dev':
                print("   运行: python scripts/start_dev.bat")
            else:
                print("   运行: python scripts/start_prod.bat")
            return 0
        else:
            print(f"❌ 切换到 {args.env.upper()} 环境失败")
            return 1


if __name__ == '__main__':
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n👋 操作被用户取消")
        exit(1)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        exit(1)
