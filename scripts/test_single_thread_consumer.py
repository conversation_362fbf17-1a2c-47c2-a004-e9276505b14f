#!/usr/bin/env python3
"""
单线程消费者测试脚本
测试单线程消费者的性能和稳定性
"""

import sys
import os
import time
import json
import requests
import threading
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class SingleThreadConsumerTester:
    """单线程消费者测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.health_url = "http://localhost:8081/health"
        self.metrics_url = "http://localhost:8001/metrics"
        self.test_duration = 180  # 3分钟测试
        self.check_interval = 10  # 10秒检查一次
        
        self.test_results = {
            'start_time': None,
            'end_time': None,
            'total_checks': 0,
            'healthy_checks': 0,
            'unhealthy_checks': 0,
            'performance_data': [],
            'error_log': []
        }
    
    def check_consumer_health(self) -> dict:
        """检查消费者健康状态"""
        try:
            response = requests.get(self.health_url, timeout=5)
            if response.status_code == 200:
                return response.json()
            else:
                return {'status': 'unhealthy', 'error': f'HTTP {response.status_code}'}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}
    
    def get_consumer_metrics(self) -> dict:
        """获取消费者指标"""
        try:
            response = requests.get(self.metrics_url, timeout=5)
            if response.status_code == 200:
                metrics = {}
                for line in response.text.split('\n'):
                    if line.startswith('#') or not line.strip():
                        continue
                    
                    parts = line.split(' ')
                    if len(parts) >= 2:
                        metric_name = parts[0]
                        try:
                            metric_value = float(parts[1])
                            metrics[metric_name] = metric_value
                        except ValueError:
                            continue
                
                return metrics
            else:
                return {}
        except Exception as e:
            return {}
    
    def run_performance_test(self):
        """运行性能测试"""
        print("🚀 开始单线程消费者性能测试")
        print("=" * 60)
        print(f"测试时长: {self.test_duration}秒")
        print(f"检查间隔: {self.check_interval}秒")
        print()
        
        self.test_results['start_time'] = datetime.now()
        start_time = time.time()
        
        # 获取初始指标
        initial_metrics = self.get_consumer_metrics()
        initial_health = self.check_consumer_health()
        
        print("📊 初始状态:")
        print(f"   健康状态: {initial_health.get('status', 'unknown')}")
        print(f"   消息处理: {initial_metrics.get('messages_processed_total', 0)}")
        print(f"   数据库类型: {initial_health.get('database', {}).get('database_type', 'unknown')}")
        print()
        
        # 开始监控循环
        while time.time() - start_time < self.test_duration:
            try:
                self.test_results['total_checks'] += 1
                
                # 检查健康状态
                health = self.check_consumer_health()
                metrics = self.get_consumer_metrics()
                
                if health.get('status') == 'healthy':
                    self.test_results['healthy_checks'] += 1
                    status_icon = "✅"
                else:
                    self.test_results['unhealthy_checks'] += 1
                    status_icon = "❌"
                    self.test_results['error_log'].append({
                        'time': datetime.now().isoformat(),
                        'error': health.get('error', 'Unknown error')
                    })
                
                # 记录性能数据
                performance_data = {
                    'timestamp': datetime.now().isoformat(),
                    'messages_processed': metrics.get('messages_processed_total', 0),
                    'messages_received': metrics.get('messages_received_total', 0),
                    'messages_failed': metrics.get('messages_failed_total', 0),
                    'consumer_status': health.get('consumer', 'unknown'),
                    'database_status': health.get('database', {}).get('status', 'unknown')
                }
                self.test_results['performance_data'].append(performance_data)
                
                # 显示当前状态
                elapsed = time.time() - start_time
                remaining = self.test_duration - elapsed
                
                # 计算消息处理速率
                current_processed = metrics.get('messages_processed_total', 0)
                initial_processed = initial_metrics.get('messages_processed_total', 0)
                processed_delta = current_processed - initial_processed
                rate = processed_delta / elapsed if elapsed > 0 else 0
                
                print(f"{status_icon} [{elapsed:6.0f}s] 健康: {health.get('status', 'unknown'):8} | "
                      f"处理: {current_processed:6.0f} | "
                      f"速率: {rate:5.1f}/s | "
                      f"剩余: {remaining:3.0f}s")
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                print("\n测试被用户中断")
                break
            except Exception as e:
                print(f"检查过程中发生错误: {e}")
                time.sleep(1)
        
        self.test_results['end_time'] = datetime.now()
        
        # 获取最终指标
        final_metrics = self.get_consumer_metrics()
        final_health = self.check_consumer_health()
        
        # 计算性能指标
        messages_processed = final_metrics.get('messages_processed_total', 0) - initial_metrics.get('messages_processed_total', 0)
        messages_received = final_metrics.get('messages_received_total', 0) - initial_metrics.get('messages_received_total', 0)
        messages_failed = final_metrics.get('messages_failed_total', 0) - initial_metrics.get('messages_failed_total', 0)
        
        # 生成报告
        self.generate_report(messages_processed, messages_received, messages_failed)
    
    def generate_report(self, messages_processed, messages_received, messages_failed):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 单线程消费者测试报告")
        print("=" * 60)
        
        # 基本统计
        duration = (self.test_results['end_time'] - self.test_results['start_time']).total_seconds()
        uptime_percentage = (self.test_results['healthy_checks'] / self.test_results['total_checks']) * 100 if self.test_results['total_checks'] > 0 else 0
        
        print(f"测试时长: {duration:.0f}秒")
        print(f"总检查次数: {self.test_results['total_checks']}")
        print(f"健康检查: {self.test_results['healthy_checks']}")
        print(f"异常检查: {self.test_results['unhealthy_checks']}")
        print(f"可用性: {uptime_percentage:.1f}%")
        print()
        
        # 性能统计
        print("📈 性能统计:")
        print(f"接收消息数: {messages_received}")
        print(f"处理消息数: {messages_processed}")
        print(f"失败消息数: {messages_failed}")
        
        if duration > 0:
            print(f"接收速率: {messages_received / duration:.1f} msg/s")
            print(f"处理速率: {messages_processed / duration:.1f} msg/s")
        
        if messages_received > 0:
            success_rate = (messages_processed / messages_received) * 100
            print(f"成功率: {success_rate:.1f}%")
        print()
        
        # 性能评估
        print("🎯 性能评估:")
        
        if duration > 0:
            processing_rate = messages_processed / duration
            
            if processing_rate >= 100:
                performance_score = "优秀"
                performance_icon = "🟢"
            elif processing_rate >= 50:
                performance_score = "良好"
                performance_icon = "🟡"
            elif processing_rate >= 10:
                performance_score = "一般"
                performance_icon = "🟠"
            else:
                performance_score = "需要改进"
                performance_icon = "🔴"
            
            print(f"{performance_icon} 处理性能: {performance_score} ({processing_rate:.1f} msg/s)")
        
        if uptime_percentage >= 95:
            stability_score = "优秀"
            stability_icon = "🟢"
        elif uptime_percentage >= 85:
            stability_score = "良好"
            stability_icon = "🟡"
        else:
            stability_score = "需要改进"
            stability_icon = "🔴"
        
        print(f"{stability_icon} 稳定性: {stability_score} ({uptime_percentage:.1f}%)")
        
        if messages_failed == 0:
            print("✅ 错误处理: 无失败消息")
        elif messages_received > 0:
            error_rate = (messages_failed / messages_received) * 100
            if error_rate < 1:
                print(f"✅ 错误处理: 良好 ({error_rate:.2f}%)")
            else:
                print(f"⚠️  错误处理: 需要关注 ({error_rate:.2f}%)")
        
        # 建议
        print("\n💡 建议:")
        if duration > 0:
            processing_rate = messages_processed / duration
            if processing_rate < 50:
                print("   - 考虑启用批量处理提高性能")
                print("   - 检查数据库连接和查询性能")
                print("   - 优化消息处理逻辑")
        
        if uptime_percentage < 90:
            print("   - 检查网络连接稳定性")
            print("   - 分析错误日志")
            print("   - 考虑增加重连机制")
        
        if messages_failed > 0:
            print("   - 分析失败消息的原因")
            print("   - 检查数据格式和验证逻辑")
            print("   - 考虑增加错误处理机制")
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"single_thread_test_report_{timestamp}.json"
        
        report_data = {
            'test_results': self.test_results,
            'performance': {
                'messages_received': messages_received,
                'messages_processed': messages_processed,
                'messages_failed': messages_failed,
                'duration': duration,
                'processing_rate': messages_processed / duration if duration > 0 else 0,
                'success_rate': (messages_processed / messages_received * 100) if messages_received > 0 else 0
            },
            'stability': {
                'uptime_percentage': uptime_percentage,
                'total_checks': self.test_results['total_checks'],
                'healthy_checks': self.test_results['healthy_checks'],
                'unhealthy_checks': self.test_results['unhealthy_checks']
            }
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 详细报告已保存到: {report_file}")


def main():
    """主函数"""
    print("🔧 单线程消费者性能测试工具")
    print("=" * 60)
    print("请确保单线程消费者正在运行:")
    print("python consumer/single_thread_main.py --env dev")
    print()
    
    # 等待用户确认
    try:
        input("消费者启动后按回车开始测试...")
    except KeyboardInterrupt:
        print("\n测试被用户取消")
        return
    
    # 运行测试
    tester = SingleThreadConsumerTester()
    
    try:
        tester.run_performance_test()
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")


if __name__ == '__main__':
    main()
