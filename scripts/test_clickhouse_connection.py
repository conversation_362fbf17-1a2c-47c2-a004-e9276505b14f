#!/usr/bin/env python3
"""
ClickHouse连接测试脚本
用于测试和配置ClickHouse连接参数
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_clickhouse_direct():
    """直接测试ClickHouse连接"""
    try:
        from clickhouse_driver import Client
    except ImportError:
        print("❌ ClickHouse驱动未安装")
        print("请运行: pip install clickhouse-driver")
        return False
    
    # 测试不同的连接配置
    test_configs = [
        {
            'name': '默认配置（无密码）',
            'host': '127.0.0.1',
            'port': 9000,
            'user': 'default',
            'password': '',
            'database': 'default'
        },
        {
            'name': '默认配置（空密码）',
            'host': '127.0.0.1',
            'port': 9000,
            'user': 'default',
            'password': None,
            'database': 'default'
        },
        {
            'name': 'Docker默认配置',
            'host': '127.0.0.1',
            'port': 9000,
            'user': 'default',
            'password': '',
            'database': 'default'
        }
    ]
    
    for config in test_configs:
        print(f"\n🔗 测试 {config['name']}...")
        try:
            client = Client(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database'],
                connect_timeout=5
            )
            
            # 测试查询
            result = client.execute("SELECT version()")
            version = result[0][0] if result else "Unknown"
            
            print(f"✅ 连接成功！")
            print(f"   ClickHouse版本: {version}")
            print(f"   主机: {config['host']}:{config['port']}")
            print(f"   用户: {config['user']}")
            print(f"   数据库: {config['database']}")
            
            # 测试创建数据库
            try:
                client.execute("CREATE DATABASE IF NOT EXISTS market_data")
                print(f"   ✅ 数据库 'market_data' 创建成功")
            except Exception as e:
                print(f"   ⚠️  创建数据库失败: {e}")
            
            client.disconnect()
            return config
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
    
    return None

def update_config_with_working_settings(working_config):
    """使用可工作的配置更新配置文件"""
    if not working_config:
        return False
    
    try:
        import yaml
        config_path = "config/database.yaml"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 更新ClickHouse配置
        config['clickhouse']['primary'].update({
            'host': working_config['host'],
            'port': working_config['port'],
            'username': working_config['user'],
            'password': working_config['password'],
            'database': 'market_data',  # 使用我们的数据库
            'compression': False
        })
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"\n✅ 配置文件已更新为可工作的设置")
        return True
        
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")
        return False

def setup_clickhouse_with_docker():
    """使用Docker设置ClickHouse"""
    print("\n🐳 Docker ClickHouse 设置指南:")
    print("=" * 50)
    
    print("1. 停止现有的ClickHouse容器（如果有）:")
    print("   docker stop clickhouse-server")
    print("   docker rm clickhouse-server")
    
    print("\n2. 启动新的ClickHouse容器:")
    print("   docker run -d \\")
    print("     --name clickhouse-server \\")
    print("     --ulimit nofile=262144:262144 \\")
    print("     -p 9000:9000 \\")
    print("     -p 8123:8123 \\")
    print("     clickhouse/clickhouse-server")
    
    print("\n3. 等待容器启动（约10-30秒）")
    
    print("\n4. 验证容器状态:")
    print("   docker ps | grep clickhouse")
    print("   docker logs clickhouse-server")
    
    print("\n5. 测试连接:")
    print("   docker exec -it clickhouse-server clickhouse-client")

def setup_clickhouse_local():
    """本地ClickHouse设置指南"""
    print("\n💻 本地 ClickHouse 设置指南:")
    print("=" * 50)
    
    print("1. 重置默认用户密码:")
    print("   sudo rm -f /etc/clickhouse-server/users.d/default-password.xml")
    
    print("\n2. 重启ClickHouse服务:")
    print("   sudo systemctl restart clickhouse-server")
    
    print("\n3. 检查服务状态:")
    print("   sudo systemctl status clickhouse-server")
    
    print("\n4. 测试连接:")
    print("   clickhouse-client")

def main():
    """主函数"""
    setup_logging()
    
    print("🚀 ClickHouse连接诊断工具")
    print("=" * 50)
    
    # 检查ClickHouse驱动
    try:
        from clickhouse_driver import Client
        print("✅ ClickHouse驱动已安装")
    except ImportError:
        print("❌ ClickHouse驱动未安装")
        print("请运行: pip install clickhouse-driver")
        return 1
    
    # 测试连接
    working_config = test_clickhouse_direct()
    
    if working_config:
        print(f"\n🎉 找到可工作的配置: {working_config['name']}")
        
        # 询问是否更新配置文件
        try:
            response = input("\n是否要用此配置更新 config/database.yaml? (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                if update_config_with_working_settings(working_config):
                    print("✅ 配置已更新，现在可以使用ClickHouse了")
                    print("\n下一步:")
                    print("1. 运行: python scripts/init_clickhouse.py")
                    print("2. 设置 database_type: clickhouse")
                    print("3. 启动消费者: python consumer/main.py")
                    return 0
        except KeyboardInterrupt:
            print("\n操作已取消")
    
    print("\n❌ 无法连接到ClickHouse")
    print("\n可能的解决方案:")
    
    # 检查是否有Docker
    import subprocess
    try:
        subprocess.run(['docker', '--version'], capture_output=True, check=True)
        print("\n🐳 检测到Docker，推荐使用Docker方式:")
        setup_clickhouse_with_docker()
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("\n💻 本地安装方式:")
        setup_clickhouse_local()
    
    print("\n📋 常见问题:")
    print("1. 确保ClickHouse服务正在运行")
    print("2. 检查端口9000是否被占用")
    print("3. 检查防火墙设置")
    print("4. 如果使用Docker，确保容器已启动")
    
    return 1

if __name__ == '__main__':
    exit(main())
