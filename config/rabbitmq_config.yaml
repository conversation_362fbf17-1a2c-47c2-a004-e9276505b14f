# RabbitMQ配置文件 - Producer服务专用
rabbitmq:
  # RabbitMQ服务器配置
  host: "127.0.0.1"
  port: 5672
  username: "admin"
  password: "admin123"
  virtual_host: "/"
  
  # 队列配置
  queue:
    name: "market_data_queue"
    durable: true  # 队列持久化
    auto_delete: false
    exclusive: false
  
  # 交换机配置
  exchange:
    name: "market_data_exchange"
    type: "direct"
    durable: true
    auto_delete: false
  
  # 路由键
  routing_key: "market.data"
  
  # 连接配置
  connection:
    heartbeat: 600  # 心跳间隔（秒）
    blocked_connection_timeout: 300  # 阻塞连接超时（秒）
    socket_timeout: 10  # socket超时（秒）
    retry_delay: 5  # 重连延迟（秒）
    max_retries: 3  # 最大重试次数

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/producer_rabbitmq.log"

# Producer应用配置
app:
  # 数据处理配置
  batch_size: 50  # 批量发送大小
  flush_interval: 2  # 刷新间隔（秒）

  # 数据格式配置
  data_format: "json"  # json 或 msgpack
  compression: false  # 是否压缩数据

  # 性能配置
  max_message_size: 1048576  # 最大消息大小（1MB）
  send_timeout: 30  # 发送超时（秒）

# 消费者配置
consumer:
  # 重试配置
  retry:
    max_attempts: 3
    delay_seconds: 5
    exponential_backoff: true

  # 批处理配置
  batch:
    enabled: true
    size: 100
    timeout_seconds: 30

  # 多线程配置
  thread_pool_size: 4        # 线程池大小，建议为CPU核心数的2倍
  batch_size: 20             # 批处理大小
  batch_timeout: 1.0         # 批处理超时（秒）
  max_queue_size: 1000       # 最大队列大小

  # 性能优化配置
  performance:
    prefetch_multiplier: 2   # 预取倍数（线程数 * 倍数）
    max_prefetch: 20         # 最大预取数量
    enable_batch_insert: true # 启用批量插入
    connection_pool_size: 10  # 数据库连接池大小
