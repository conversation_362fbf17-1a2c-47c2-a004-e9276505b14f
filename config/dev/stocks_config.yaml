# 开发环境股票订阅配置文件
stocks:
  # 开发环境默认订阅的股票列表（较少股票用于测试）
  default_list:
    # 上海证券交易所股票（精选几只用于开发测试）
    - '600030.SH'  # 中信证券
    - '600999.SH'  # 招商证券
    - '601211.SH'  # 国泰君安
    - '601688.SH'  # 华泰证券
    - '601995.SH'  # 中金公司
    
    # 深圳证券交易所股票（精选几只用于开发测试）
    - '000776.SZ'  # 广发证券
    - '002736.SZ'  # 国信证券
    - '300059.SZ'  # 东方财富

  # 开发测试用股票组
  test_group:
    - '600030.SH'  # 中信证券
    - '000776.SZ'  # 广发证券
    
  # 银行股（开发环境精简）
  banking:
    - '600036.SH'  # 招商银行
    - '000001.SZ'  # 平安银行

  # 科技股（开发环境精简）
  technology:
    - '002415.SZ'  # 海康威视
    - '300750.SZ'  # 宁德时代

  # 完整股票列表（用于压力测试）
  full_list:
    # 上海证券交易所股票
    - '600030.SH'  # 中信证券
    - '600061.SH'  # 国投资本
    - '600109.SH'  # 国金证券
    - '600918.SH'  # 中泰证券
    - '600958.SH'  # 东方证券
    - '600999.SH'  # 招商证券
    - '601059.SH'  # 古井贡酒
    - '601066.SH'  # 中信建投
    - '601108.SH'  # 财通证券
    - '601136.SH'  # 首创股份
    - '601211.SH'  # 国泰君安
    - '601236.SH'  # 红塔证券
    - '601377.SH'  # 兴业证券
    - '601456.SH'  # 国联证券
    - '601555.SH'  # 东吴证券
    - '601688.SH'  # 华泰证券
    - '601788.SH'  # 光大证券
    - '601878.SH'  # 浙商证券
    - '601881.SH'  # 中国银河
    - '601901.SH'  # 方正证券
    - '601990.SH'  # 南京证券
    - '601995.SH'  # 中金公司
    
    # 深圳证券交易所股票
    - '000166.SZ'  # 申万宏源
    - '000776.SZ'  # 广发证券
    - '000783.SZ'  # 长江证券
    - '002673.SZ'  # 西部证券
    - '002736.SZ'  # 国信证券
    - '002939.SZ'  # 长城证券
    - '002945.SZ'  # 华林证券
    - '300059.SZ'  # 东方财富

# 订阅配置
subscription:
  # 开发环境使用测试组
  active_group: "test_group"  # 开发环境默认使用少量股票
  
  # 订阅参数
  period: "tick"        # 订阅周期
  count: 0            # 数据条数 (0表示实时推送)
  
  # 性能配置（开发环境优化）
  batch_size: 5       # 开发环境较小批量
  delay_between_batches: 1.0  # 开发环境较长延迟
  delay_between_stocks: 0.2   # 开发环境较长延迟
  
  # 开发环境特殊配置
  development:
    # 限制订阅数量
    max_stocks: 10
    
    # 测试模式
    test_mode: true
    
    # 模拟数据
    mock_data: false

# 过滤配置
filters:
  # 开发环境启用过滤
  enabled: true
  
  # 市场过滤 (SH, SZ)
  markets: ["SH", "SZ"]
  
  # 股票代码前缀过滤（开发环境限制）
  code_prefixes: ["600", "000", "002", "300"]
  
  # 开发环境特殊过滤
  development_filters:
    # 只允许特定股票
    whitelist_enabled: true
    whitelist: ["600030.SH", "000776.SZ", "600999.SH", "002736.SZ"]
    
    # 排除某些股票
    blacklist_enabled: false
    blacklist: []

# 开发环境配置
development:
  # 调试模式
  debug_mode: true
  
  # 详细日志
  verbose_logging: true
  
  # 测试数据生成
  test_data_generation:
    enabled: false
    interval: 10  # 10秒生成一次测试数据
    
  # 性能测试
  performance_testing:
    enabled: false
    duration: 60  # 60秒性能测试
    
  # 环境标识
  environment: "development"
  instance_id: "dev-001"
