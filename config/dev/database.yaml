# 开发环境数据库配置
# 数据库类型选择: mysql, clickhouse
database_type: clickhouse

# 双写模式配置
dual_write:
  enabled: false  # 开发环境默认关闭双写
  primary: mysql
  secondary: clickhouse
  fail_on_secondary_error: false

# MySQL配置
mysql:
  # 主数据库配置
  primary:
    host: "127.0.0.1"
    port: 3306
    username: "root"
    password: "123456"
    database: "market_data_dev"  # 开发环境数据库
    charset: "utf8mb4"
    
  # 连接池配置
  pool:
    min_connections: 2  # 开发环境较少连接
    max_connections: 10
    max_idle_time: 300
    max_lifetime: 3600
    
  # 连接配置
  connection:
    connect_timeout: 10
    read_timeout: 30
    write_timeout: 30
    autocommit: true
    
  # SSL配置
  ssl:
    enabled: false
    
  # 备用数据库配置（开发环境可选）
  backup:
    enabled: false
    host: "127.0.0.1"
    port: 3306
    username: "root"
    password: "123456"
    database: "market_data_dev_backup"

# ClickHouse配置
clickhouse:
  # 主数据库配置
  primary:
    host: "127.0.0.1"
    port: 9000
    username: "admin"
    password: "SecurePass123!"
    database: "market_data"  # 开发环境数据库
    connect_timeout: 10
    send_receive_timeout: 300
    compression: false  # 开发环境关闭压缩

    # 连接池配置
  pool:
    min_connections: 1
    max_connections: 5  # 开发环境较少连接
    max_idle_time: 300

  # 批量插入配置
  batch:
    size: 500                    # 批量大小（开发环境较小）
    timeout: 3.0                 # 批量超时（秒）
    max_memory_mb: 50            # 最大内存使用（MB）
    auto_flush: true             # 自动刷新
    compression: false           # 开发环境关闭压缩
    parallel_inserts: 1          # 并行插入线程数（开发环境单线程）

    # 高级配置
    settings:
      max_insert_block_size: 1048576      # 最大插入块大小
      min_insert_block_size_rows: 1000    # 最小插入行数
      min_insert_block_size_bytes: 268435456  # 最小插入字节数
      insert_quorum: 0                    # 插入仲裁数
      insert_quorum_timeout: 60000        # 插入仲裁超时（毫秒）

  # 备用数据库配置
  backup:
    enabled: false

# 数据分区配置
partitioning:
  enabled: true
  # 分区策略: daily, weekly, monthly
  strategy: daily
  
  # 分区保留策略
  retention:
    # 开发环境保留较短时间
    market_quotes: 7    # 7天
    market_depth: 7     # 7天
    processing_log: 3   # 3天
    
  # 自动分区管理
  auto_management:
    enabled: true
    check_interval: 3600  # 1小时检查一次
    
# 性能配置
performance:
  # 批量插入配置
  batch_insert:
    enabled: true
    batch_size: 100     # 开发环境较小批次
    timeout: 5          # 5秒超时
    
  # 索引配置
  indexes:
    auto_create: true
    
  # 缓存配置
  cache:
    enabled: false      # 开发环境关闭缓存
    
# 日志配置
logging:
  # 数据库操作日志
  database_operations: true
  slow_query_threshold: 1.0  # 1秒慢查询阈值
  
  # 连接池日志
  connection_pool: true
  
# 监控配置
monitoring:
  enabled: true
  metrics_collection: true
  health_check_interval: 30  # 30秒检查一次
