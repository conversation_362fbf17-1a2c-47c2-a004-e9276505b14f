# 开发环境RabbitMQ配置文件
rabbitmq:
  # RabbitMQ服务器配置
  host: "127.0.0.1"
  port: 5672
  username: "admin"
  password: "admin123"
  virtual_host: "/"  # 开发环境虚拟主机
  
  # 队列配置
  queue:
    name: "market_data_queue"  # 开发环境队列
    durable: true
    auto_delete: false
    exclusive: false
    arguments:
      x-max-length: 10000  # 开发环境较小队列限制
      x-message-ttl: 3600000  # 1小时TTL
  
  # 交换机配置
  exchange:
    name: "market_data_exchange"  # 开发环境交换机
    type: "direct"
    durable: true
    auto_delete: false
  
  # 路由键
  routing_key: "market.data"
  
  # 连接配置
  connection:
    heartbeat: 300  # 5分钟心跳，更稳定
    blocked_connection_timeout: 300
    socket_timeout: 30  # 增加socket超时
    retry_delay: 5
    max_retries: 5  # 增加重试次数
    connection_attempts: 3

    # TCP配置
    tcp_options:
      TCP_KEEPIDLE: 600
      TCP_KEEPINTVL: 30
      TCP_KEEPCNT: 3

    # 连接池配置
    pool:
      max_connections: 5  # 开发环境较少连接
      max_channels_per_connection: 10

# 日志配置
logging:
  level: "DEBUG"  # 开发环境详细日志
  format: "%(asctime)s - %(name)s - %(levelname)s - [%(thread)d] - %(message)s"
  file: "logs/dev_rabbitmq.log"
  
  # 日志轮转
  rotation:
    max_size: "10MB"
    backup_count: 3

# Producer应用配置
app:
  # 数据处理配置
  batch_size: 20  # 开发环境较小批次
  flush_interval: 5  # 开发环境较长间隔
  
  # 数据格式配置
  data_format: "json"
  compression: false  # 开发环境关闭压缩
  
  # 性能配置
  max_message_size: 524288  # 512KB，开发环境较小
  send_timeout: 30
  
  # 重试配置
  retry:
    max_attempts: 3
    delay_seconds: 2
    exponential_backoff: true

# 消费者配置
consumer:
  # 重试配置
  retry:
    max_attempts: 3
    delay_seconds: 5
    exponential_backoff: true
  
  # 批处理配置
  batch:
    enabled: true
    size: 50  # 开发环境较小批次
    timeout_seconds: 10
  
  # 多线程配置
  thread_pool_size: 4  # 开发环境较少线程
  batch_size: 20
  batch_timeout: 2.0
  max_queue_size: 500  # 开发环境较小队列

  # 性能优化配置
  performance:
    prefetch_multiplier: 1  # 降低预取倍数，提高稳定性
    max_prefetch: 10  # 开发环境保守预取
    enable_batch_insert: true
    connection_pool_size: 5

  # 稳定性配置
  stability:
    auto_reconnect: true
    max_reconnect_attempts: 5
    reconnect_delay: 5  # 秒
    connection_check_interval: 30  # 秒
    batch_ack: true  # 启用批量确认

# 监控配置
monitoring:
  enabled: true
  
  # 指标配置
  metrics:
    enabled: true
    port: 8000  # 开发环境指标端口
    path: "/metrics"
    
  # 健康检查配置
  health_check:
    enabled: true
    port: 8080  # 开发环境健康检查端口
    path: "/health"
    interval: 30
    
  # 告警配置
  alerts:
    enabled: false  # 开发环境关闭告警
    
# 开发环境特殊配置
development:
  # 调试模式
  debug_mode: true
  
  # 详细日志
  verbose_logging: true
  
  # 性能分析
  profiling:
    enabled: false
    
  # 测试数据
  test_data:
    enabled: true
    auto_generate: false
