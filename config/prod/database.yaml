# 生产环境数据库配置
# 数据库类型选择: mysql, clickhouse
database_type: clickhouse

# 双写模式配置
dual_write:
  enabled: false  # 生产环境启用双写
  primary: mysql
  secondary: clickhouse
  fail_on_secondary_error: false

# MySQL配置
mysql:
  # 主数据库配置
  primary:
    host: "*************"
    port: 3306
    username: "root"
    password: "quant_msq_2025"
    database: "market_data"
    charset: "utf8mb4"
    
  # 连接池配置
  pool:
    min_connections: 10  # 生产环境更多连接
    max_connections: 50
    max_idle_time: 600
    max_lifetime: 7200
    
  # 连接配置
  connection:
    connect_timeout: 5   # 生产环境更短超时
    read_timeout: 60
    write_timeout: 60
    autocommit: true
    
  # SSL配置
  ssl:
    enabled: true
    ca_cert: "/etc/ssl/certs/mysql-ca.pem"
    client_cert: "/etc/ssl/certs/mysql-client.pem"
    client_key: "/etc/ssl/private/mysql-client-key.pem"
      
  # 备用数据库配置
  backup:
    enabled: true
    host: "prod-mysql-slave.internal"  # 生产环境从库
    port: 3306
    username: "market_data_user"
    password: "${MYSQL_PASSWORD}"
    database: "market_data_prod"

# ClickHouse配置
clickhouse:
  # 主数据库配置
  primary:
    host: "*************"
    port: 9001
    username: "admin"
    password: "SecurePass123!"
    database: "market_data"
    connect_timeout: 10
    send_receive_timeout: 300
    compression: false  # 生产环境启用压缩
    
  # 连接池配置
  pool:
    min_connections: 5
    max_connections: 20  # 生产环境更多连接
    max_idle_time: 600

  # 批量插入配置
  batch:
    size: 2000                   # 批量大小（生产环境大批次）
    timeout: 1.0                 # 批量超时（秒）
    max_memory_mb: 200           # 最大内存使用（MB）
    auto_flush: true             # 自动刷新
    compression: true            # 生产环境启用压缩
    parallel_inserts: 4          # 并行插入线程数

    # 高级配置
    settings:
      max_insert_block_size: 1048576      # 最大插入块大小
      min_insert_block_size_rows: 5000    # 最小插入行数
      min_insert_block_size_bytes: 268435456  # 最小插入字节数
      insert_quorum: 1                    # 插入仲裁数（生产环境保证一致性）
      insert_quorum_timeout: 60000        # 插入仲裁超时（毫秒）

      # 性能优化设置
      max_threads: 8                      # 最大线程数
      max_memory_usage: 10000000000       # 最大内存使用（10GB）
      distributed_product_mode: 'global'  # 分布式产品模式

  # 备用数据库配置
  backup:
    enabled: true
    host: "prod-clickhouse-backup.internal"
    port: 9000
    username: "market_data_user"
    password: "${CLICKHOUSE_PASSWORD}"
    database: "market_data_prod"

# 数据分区配置
partitioning:
  enabled: true
  # 分区策略: daily, weekly, monthly
  strategy: daily
  
  # 分区保留策略
  retention:
    # 生产环境保留更长时间
    market_quotes: 90    # 90天
    market_depth: 30     # 30天
    processing_log: 30   # 30天
    
  # 自动分区管理
  auto_management:
    enabled: true
    check_interval: 1800  # 30分钟检查一次
    
# 性能配置
performance:
  # 批量插入配置
  batch_insert:
    enabled: true
    batch_size: 1000    # 生产环境大批次
    timeout: 30         # 30秒超时
    
  # 索引配置
  indexes:
    auto_create: true
    
  # 缓存配置
  cache:
    enabled: true       # 生产环境启用缓存
    size: "512MB"
    ttl: 3600
    
# 安全配置
security:
  # 数据加密
  encryption:
    enabled: true
    key_rotation_interval: 86400  # 24小时
    
  # 访问控制
  access_control:
    enabled: true
    max_connections_per_user: 10
    
  # 审计日志
  audit:
    enabled: true
    log_all_queries: false
    log_slow_queries: true
    
# 日志配置
logging:
  # 数据库操作日志
  database_operations: false  # 生产环境关闭详细日志
  slow_query_threshold: 5.0   # 5秒慢查询阈值
  
  # 连接池日志
  connection_pool: false
  
# 监控配置
monitoring:
  enabled: true
  metrics_collection: true
  health_check_interval: 10  # 10秒检查一次
  
  # 告警配置
  alerts:
    enabled: true
    thresholds:
      connection_pool_usage: 80  # 80%连接池使用率告警
      slow_query_count: 10       # 10个慢查询告警
      error_rate: 5              # 5%错误率告警
      
# 备份配置
backup:
  enabled: true
  
  # 自动备份
  auto_backup:
    enabled: true
    schedule: "0 2 * * *"  # 每天凌晨2点
    retention_days: 30
    
  # 备份存储
  storage:
    type: "s3"
    bucket: "market-data-backups"
    region: "us-east-1"
    
# 高可用配置
high_availability:
  enabled: true
  
  # 故障转移
  failover:
    enabled: true
    timeout: 30
    max_retries: 3
    
  # 负载均衡
  load_balancing:
    enabled: true
    strategy: "round_robin"
    
# 环境标识
environment:
  name: "production"
  region: "us-east-1"
  instance_id: "${INSTANCE_ID}"
  deployment_id: "${DEPLOYMENT_ID}"
