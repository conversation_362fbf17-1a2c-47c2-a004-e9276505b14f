# 生产环境股票订阅配置文件
stocks:
  # 生产环境完整股票列表
  default_list:
    # 上海证券交易所股票
    - '600030.SH'  # 中信证券
    - '600061.SH'  # 国投资本
    - '600109.SH'  # 国金证券
    - '600918.SH'  # 中泰证券
    - '600958.SH'  # 东方证券
    - '600999.SH'  # 招商证券
    - '601059.SH'  # 古井贡酒
    - '601066.SH'  # 中信建投
    - '601108.SH'  # 财通证券
    - '601136.SH'  # 首创股份
    - '601211.SH'  # 国泰君安
    - '601236.SH'  # 红塔证券
    - '601377.SH'  # 兴业证券
    - '601456.SH'  # 国联证券
    - '601555.SH'  # 东吴证券
    - '601688.SH'  # 华泰证券
    - '601788.SH'  # 光大证券
    - '601878.SH'  # 浙商证券
    - '601881.SH'  # 中国银河
    - '601901.SH'  # 方正证券
    - '601990.SH'  # 南京证券
    - '601995.SH'  # 中金公司
    
    # 深圳证券交易所股票
    - '000166.SZ'  # 申万宏源
    - '000776.SZ'  # 广发证券
    - '000783.SZ'  # 长江证券
    - '002673.SZ'  # 西部证券
    - '002736.SZ'  # 国信证券
    - '002939.SZ'  # 长城证券
    - '002945.SZ'  # 华林证券
    - '300059.SZ'  # 东方财富

  # 银行股组合
  banking:
    - '600000.SH'  # 浦发银行
    - '600036.SH'  # 招商银行
    - '601166.SH'  # 兴业银行
    - '601328.SH'  # 交通银行
    - '601398.SH'  # 工商银行
    - '601939.SH'  # 建设银行
    - '601988.SH'  # 中国银行
    - '000001.SZ'  # 平安银行
    - '002142.SZ'  # 宁波银行
    - '002839.SZ'  # 张家港行

  # 科技股组合
  technology:
    - '000858.SZ'  # 五粮液
    - '002415.SZ'  # 海康威视
    - '002475.SZ'  # 立讯精密
    - '300750.SZ'  # 宁德时代
    - '688981.SH'  # 中芯国际
    - '688599.SH'  # 天合光能
    - '688036.SH'  # 传音控股
    - '688111.SH'  # 金山办公

  # 蓝筹股组合
  blue_chips:
    - '000002.SZ'  # 万科A
    - '000858.SZ'  # 五粮液
    - '600519.SH'  # 贵州茅台
    - '600036.SH'  # 招商银行
    - '000001.SZ'  # 平安银行
    - '601318.SH'  # 中国平安
    - '600276.SH'  # 恒瑞医药
    - '002415.SZ'  # 海康威视

  # 高频交易股票（流动性好）
  high_frequency:
    - '600030.SH'  # 中信证券
    - '600999.SH'  # 招商证券
    - '601211.SH'  # 国泰君安
    - '601688.SH'  # 华泰证券
    - '000776.SZ'  # 广发证券
    - '002736.SZ'  # 国信证券
    - '300059.SZ'  # 东方财富

# 订阅配置
subscription:
  # 生产环境使用完整列表
  active_group: "default_list"
  
  # 订阅参数
  period: "tick"        # 订阅周期
  count: 0            # 数据条数 (0表示实时推送)
  
  # 性能配置（生产环境优化）
  batch_size: 20      # 生产环境批量订阅
  delay_between_batches: 0.1  # 生产环境短延迟
  delay_between_stocks: 0.05  # 生产环境短延迟
  
  # 生产环境配置
  production:
    # 最大订阅数量
    max_stocks: 1000
    
    # 并发订阅
    concurrent_subscriptions: 10
    
    # 重试配置
    retry:
      max_attempts: 5
      delay_seconds: 1
      exponential_backoff: true

# 过滤配置
filters:
  # 生产环境启用过滤
  enabled: true
  
  # 市场过滤 (SH, SZ)
  markets: ["SH", "SZ"]
  
  # 股票代码前缀过滤
  code_prefixes: ["600", "601", "000", "002", "300", "688"]
  
  # 生产环境过滤规则
  production_filters:
    # 排除ST股票
    exclude_st: true
    
    # 排除退市股票
    exclude_delisted: true
    
    # 最小市值过滤（亿元）
    min_market_cap: 10
    
    # 最小日均成交量过滤（万元）
    min_daily_volume: 1000

# 监控配置
monitoring:
  enabled: true
  
  # 订阅监控
  subscription_monitoring:
    enabled: true
    check_interval: 60  # 60秒检查一次
    
    # 告警阈值
    alerts:
      subscription_failure_rate: 5  # 5%失败率告警
      data_delay_threshold: 10      # 10秒延迟告警
      missing_data_threshold: 60    # 60秒无数据告警
  
  # 性能监控
  performance_monitoring:
    enabled: true
    metrics_collection: true
    
# 负载均衡配置
load_balancing:
  enabled: true
  
  # 股票分组策略
  grouping_strategy: "hash"  # hash, round_robin, random
  
  # 分组数量
  group_count: 4
  
  # 每组最大股票数
  max_stocks_per_group: 250

# 故障恢复配置
fault_tolerance:
  enabled: true
  
  # 自动重连
  auto_reconnect:
    enabled: true
    max_attempts: 10
    delay_seconds: 5
    
  # 数据补偿
  data_compensation:
    enabled: true
    max_compensation_time: 300  # 5分钟
    
# 数据质量配置
data_quality:
  enabled: true
  
  # 数据验证
  validation:
    enabled: true
    
    # 价格合理性检查
    price_validation:
      enabled: true
      max_change_percent: 20  # 20%涨跌幅限制
      
    # 时间戳验证
    timestamp_validation:
      enabled: true
      max_delay_seconds: 60
      
  # 数据清洗
  cleaning:
    enabled: true
    remove_duplicates: true
    fix_timestamps: true

# 环境标识
environment:
  name: "production"
  region: "us-east-1"
  instance_id: "${INSTANCE_ID}"
  deployment_version: "${DEPLOYMENT_VERSION}"
  
# 生产环境特殊配置
production:
  # 严格模式
  strict_mode: true
  
  # 详细日志（生产环境关闭）
  verbose_logging: false
  
  # 性能优化
  performance_optimization: true
  
  # 资源限制
  resource_limits:
    max_memory_usage: "2GB"
    max_cpu_usage: 80  # 80%
    
  # 安全配置
  security:
    enable_encryption: true
    audit_logging: true
