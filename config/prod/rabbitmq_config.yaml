# 生产环境RabbitMQ配置文件
rabbitmq:
  # RabbitMQ集群配置
  host: "*************"
  port: 5672
  username: "jk_rbmq_2015"
  password: "pass_jk_rbmq_2015"
  virtual_host: "/"
  
  # SSL配置
  ssl:
    enabled: true
    ca_cert: "/etc/ssl/certs/rabbitmq-ca.pem"
    cert_file: "/etc/ssl/certs/rabbitmq-client.pem"
    key_file: "/etc/ssl/private/rabbitmq-client-key.pem"
    verify_mode: "verify_peer"
  
  # 队列配置
  queue:
    name: "market_data_queue"  # 生产环境队列
    durable: true
    auto_delete: false
    exclusive: false
    arguments:
      x-max-length: 100000  # 生产环境大队列
      x-message-ttl: ********  # 24小时TTL
      x-max-priority: 10  # 支持优先级
      x-queue-mode: "lazy"  # 懒加载模式
  
  # 交换机配置
  exchange:
    name: "market_data_exchange"  # 生产环境交换机
    type: "direct"  # 与现有交换机类型保持一致
    durable: true
    auto_delete: false
    arguments:
      alternate-exchange: "market_data_dlx"  # 死信交换机
  
  # 死信队列配置
  dead_letter:
    enabled: true
    exchange: "market_data_dlx"
    queue: "market_data_dlq"
    routing_key: "market.data.failed"
  
  # 路由键 (direct类型不支持通配符)
  routing_key: "market.data.prod"
  
  # 连接配置
  connection:
    heartbeat: 600  # 生产环境长心跳
    blocked_connection_timeout: 600
    socket_timeout: 30
    retry_delay: 10
    max_retries: 5
    
    # 连接池配置
    pool:
      max_connections: 20  # 生产环境更多连接
      max_channels_per_connection: 20
      
    # 高可用配置
    ha_policy: "all"  # 所有节点镜像

# 日志配置
logging:
  level: "INFO"  # 生产环境标准日志级别
  format: "%(asctime)s - %(name)s - %(levelname)s - [%(process)d:%(thread)d] - %(message)s"
  file: "logs/prod_rabbitmq.log"
  
  # 日志轮转
  rotation:
    max_size: "100MB"
    backup_count: 10
    
  # 结构化日志
  structured_logging:
    enabled: true
    format: "json"

# Producer应用配置
app:
  # 数据处理配置
  batch_size: 100  # 生产环境大批次
  flush_interval: 1  # 生产环境短间隔
  
  # 数据格式配置
  data_format: "json"
  compression: true  # 生产环境启用压缩
  compression_level: 6
  
  # 性能配置
  max_message_size: 2097152  # 2MB，生产环境更大
  send_timeout: 60
  
  # 重试配置
  retry:
    max_attempts: 5
    delay_seconds: 5
    exponential_backoff: true
    max_delay: 300
    
  # 确认模式
  confirm_delivery: true
  mandatory: true

# 消费者配置
consumer:
  # 重试配置
  retry:
    max_attempts: 5
    delay_seconds: 10
    exponential_backoff: true
    max_delay: 600
  
  # 批处理配置
  batch:
    enabled: true
    size: 200  # 生产环境大批次
    timeout_seconds: 5
  
  # 多线程配置
  thread_pool_size: 16  # 生产环境更多线程
  batch_size: 100
  batch_timeout: 0.5
  max_queue_size: 5000  # 生产环境大队列
  
  # 性能优化配置
  performance:
    prefetch_multiplier: 3
    max_prefetch: 100  # 生产环境更多预取
    enable_batch_insert: true
    connection_pool_size: 20
    
  # QoS配置
  qos:
    prefetch_count: 100
    prefetch_size: 0
    global_qos: false

# 监控配置
monitoring:
  enabled: true
  
  # 指标配置
  metrics:
    enabled: true
    port: 9090  # 生产环境指标端口
    path: "/metrics"
    
    # 详细指标
    detailed_metrics: true
    
  # 健康检查配置
  health_check:
    enabled: true
    port: 8080  # 生产环境健康检查端口
    path: "/health"
    interval: 10  # 更频繁的健康检查
    timeout: 5
    
  # 告警配置
  alerts:
    enabled: true
    
    # 告警阈值
    thresholds:
      queue_depth: 10000
      consumer_lag: 1000
      error_rate: 1  # 1%错误率
      connection_failures: 5
      
    # 告警通知
    notifications:
      slack:
        enabled: true
        webhook_url: "${SLACK_WEBHOOK_URL}"
      email:
        enabled: true
        smtp_server: "smtp.company.com"
        recipients: ["<EMAIL>"]

# 安全配置
security:
  # 访问控制
  access_control:
    enabled: true
    
  # 消息加密
  message_encryption:
    enabled: true
    algorithm: "AES-256-GCM"
    key_rotation_interval: 86400
    
  # 审计日志
  audit:
    enabled: true
    log_all_operations: true

# 性能调优
performance:
  # TCP配置
  tcp:
    nodelay: true
    keepalive: true
    
  # 内存配置
  memory:
    high_watermark: 0.6  # 60%内存水位
    
  # 磁盘配置
  disk:
    free_limit: "10GB"

# 备份和恢复
backup:
  enabled: true
  
  # 定义备份
  definitions:
    enabled: true
    schedule: "0 3 * * *"  # 每天凌晨3点
    
  # 消息备份
  messages:
    enabled: false  # 通常不备份消息
    
# 环境标识
environment:
  name: "production"
  region: "us-east-1"
  cluster_id: "${CLUSTER_ID}"
  instance_id: "${INSTANCE_ID}"
  
# 生产环境特殊配置
production:
  # 严格模式
  strict_mode: true
  
  # 性能监控
  performance_monitoring: true
  
  # 自动扩缩容
  auto_scaling:
    enabled: true
    min_consumers: 2
    max_consumers: 10
    scale_up_threshold: 1000  # 队列深度
    scale_down_threshold: 100
