-- 股票行情数据库表结构设计
-- 基于报文结构: {'600000.SH': {'time': 1750316403000, 'timetag': '20250619 15:00:03', ...}}

-- 创建数据库
CREATE DATABASE IF NOT EXISTS market_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE market_data;

-- 1. 股票基本信息表
CREATE TABLE IF NOT EXISTS stock_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(50) NOT NULL UNIQUE COMMENT '股票代码',
    name VARCHAR(100) COMMENT '股票名称',
    market VARCHAR(20) COMMENT '市场(SH/SZ)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol (symbol),
    INDEX idx_market (market)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票基本信息表';

-- 2. 实时行情主表 (按天分区)
CREATE TABLE IF NOT EXISTS market_quotes (
    id BIGINT AUTO_INCREMENT,
    symbol VARCHAR(20) NOT NULL COMMENT '股票代码',
    time BIGINT NOT NULL COMMENT '时间戳(毫秒)',
    timetag VARCHAR(20) NOT NULL COMMENT '时间标签',
    last_price DECIMAL(10,3) COMMENT '最新价',
    open_price DECIMAL(10,3) COMMENT '开盘价',
    high_price DECIMAL(10,3) COMMENT '最高价',
    low_price DECIMAL(10,3) COMMENT '最低价',
    last_close DECIMAL(10,3) COMMENT '昨收价',
    amount BIGINT COMMENT '成交金额',
    volume BIGINT COMMENT '成交量',
    pvolume BIGINT COMMENT '总成交量',
    stock_status INT COMMENT '股票状态',
    open_int INT COMMENT '持仓量',
    settlement_price DECIMAL(10,3) COMMENT '结算价',
    last_settlement_price DECIMAL(10,3) COMMENT '昨结算价',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id, created_at),
    INDEX idx_symbol_time (symbol, time),
    INDEX idx_time (time),
    INDEX idx_timetag (timetag),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_symbol_time (symbol, time, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时行情主表'
PARTITION BY RANGE (TO_DAYS(created_at)) (
    PARTITION p20250101 VALUES LESS THAN (TO_DAYS('2025-01-02')),
    PARTITION p20250102 VALUES LESS THAN (TO_DAYS('2025-01-03')),
    PARTITION p20250103 VALUES LESS THAN (TO_DAYS('2025-01-04')),
    PARTITION p20250104 VALUES LESS THAN (TO_DAYS('2025-01-05')),
    PARTITION p20250105 VALUES LESS THAN (TO_DAYS('2025-01-06')),
    PARTITION p20250106 VALUES LESS THAN (TO_DAYS('2025-01-07')),
    PARTITION p20250107 VALUES LESS THAN (TO_DAYS('2025-01-08')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 3. 买卖盘口数据表 (按天分区)
CREATE TABLE IF NOT EXISTS market_depth (
    id BIGINT AUTO_INCREMENT,
    symbol VARCHAR(20) NOT NULL COMMENT '股票代码',
    time BIGINT NOT NULL COMMENT '时间戳(毫秒)',
    timetag VARCHAR(20) NOT NULL COMMENT '时间标签',

    -- 卖盘价格 (ask_price)
    ask_price_1 DECIMAL(10,3) COMMENT '卖一价',
    ask_price_2 DECIMAL(10,3) COMMENT '卖二价',
    ask_price_3 DECIMAL(10,3) COMMENT '卖三价',
    ask_price_4 DECIMAL(10,3) COMMENT '卖四价',
    ask_price_5 DECIMAL(10,3) COMMENT '卖五价',

    -- 买盘价格 (bid_price)
    bid_price_1 DECIMAL(10,3) COMMENT '买一价',
    bid_price_2 DECIMAL(10,3) COMMENT '买二价',
    bid_price_3 DECIMAL(10,3) COMMENT '买三价',
    bid_price_4 DECIMAL(10,3) COMMENT '买四价',
    bid_price_5 DECIMAL(10,3) COMMENT '买五价',

    -- 卖盘量 (ask_vol)
    ask_vol_1 BIGINT COMMENT '卖一量',
    ask_vol_2 BIGINT COMMENT '卖二量',
    ask_vol_3 BIGINT COMMENT '卖三量',
    ask_vol_4 BIGINT COMMENT '卖四量',
    ask_vol_5 BIGINT COMMENT '卖五量',

    -- 买盘量 (bid_vol)
    bid_vol_1 BIGINT COMMENT '买一量',
    bid_vol_2 BIGINT COMMENT '买二量',
    bid_vol_3 BIGINT COMMENT '买三量',
    bid_vol_4 BIGINT COMMENT '买四量',
    bid_vol_5 BIGINT COMMENT '买五量',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (id, created_at),
    INDEX idx_symbol_time (symbol, time),
    INDEX idx_time (time),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_symbol_time (symbol, time, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='买卖盘口数据表'
PARTITION BY RANGE (TO_DAYS(created_at)) (
    PARTITION p20250101 VALUES LESS THAN (TO_DAYS('2025-01-02')),
    PARTITION p20250102 VALUES LESS THAN (TO_DAYS('2025-01-03')),
    PARTITION p20250103 VALUES LESS THAN (TO_DAYS('2025-01-04')),
    PARTITION p20250104 VALUES LESS THAN (TO_DAYS('2025-01-05')),
    PARTITION p20250105 VALUES LESS THAN (TO_DAYS('2025-01-06')),
    PARTITION p20250106 VALUES LESS THAN (TO_DAYS('2025-01-07')),
    PARTITION p20250107 VALUES LESS THAN (TO_DAYS('2025-01-08')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 4. 数据处理日志表 (按天分区)
CREATE TABLE IF NOT EXISTS processing_log (
    id BIGINT AUTO_INCREMENT,
    message_id VARCHAR(100) COMMENT '消息ID',
    symbol VARCHAR(20) COMMENT '股票代码',
    processing_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    status ENUM('SUCCESS', 'FAILED', 'RETRY') DEFAULT 'SUCCESS' COMMENT '处理状态',
    error_message TEXT COMMENT '错误信息',
    raw_data JSON COMMENT '原始数据',

    PRIMARY KEY (id, processing_time),
    INDEX idx_symbol (symbol),
    INDEX idx_status (status),
    INDEX idx_processing_time (processing_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据处理日志表'
PARTITION BY RANGE (TO_DAYS(processing_time)) (
    PARTITION p20250101 VALUES LESS THAN (TO_DAYS('2025-01-02')),
    PARTITION p20250102 VALUES LESS THAN (TO_DAYS('2025-01-03')),
    PARTITION p20250103 VALUES LESS THAN (TO_DAYS('2025-01-04')),
    PARTITION p20250104 VALUES LESS THAN (TO_DAYS('2025-01-05')),
    PARTITION p20250105 VALUES LESS THAN (TO_DAYS('2025-01-06')),
    PARTITION p20250106 VALUES LESS THAN (TO_DAYS('2025-01-07')),
    PARTITION p20250107 VALUES LESS THAN (TO_DAYS('2025-01-08')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 5. 系统监控表
CREATE TABLE IF NOT EXISTS system_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(15,6) COMMENT '指标值',
    metric_type ENUM('COUNTER', 'GAUGE', 'HISTOGRAM') DEFAULT 'GAUGE' COMMENT '指标类型',
    labels JSON COMMENT '标签',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    
    INDEX idx_metric_name (metric_name),
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统监控指标表';

-- 创建分区表（按日期分区，提高查询性能）
-- 注意：MySQL分区需要根据实际需求调整

-- 插入一些测试数据
INSERT INTO stock_info (symbol, name, market) VALUES 
('600000.SH', '浦发银行', 'SH'),
('000001.SZ', '平安银行', 'SZ'),
('000002.SZ', '万科A', 'SZ')
ON DUPLICATE KEY UPDATE name=VALUES(name), market=VALUES(market);

-- 创建视图：最新行情视图
CREATE OR REPLACE VIEW latest_quotes AS
SELECT
    mq.*,
    si.name as stock_name,
    si.market
FROM market_quotes mq
INNER JOIN stock_info si ON mq.symbol = si.symbol
INNER JOIN (
    SELECT symbol, MAX(time) as max_time
    FROM market_quotes
    GROUP BY symbol
) latest ON mq.symbol = latest.symbol AND mq.time = latest.max_time;

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- 创建自动分区维护事件（每天凌晨2点执行）
CREATE EVENT IF NOT EXISTS auto_partition_maintenance
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '02:00:00')
DO
BEGIN
    -- 维护分区：保留30天数据，提前创建7天分区
    CALL MaintainPartitions(30, 7);
END;

-- 创建分区状态查询视图
CREATE OR REPLACE VIEW partition_status AS
SELECT
    TABLE_NAME,
    PARTITION_NAME,
    PARTITION_DESCRIPTION,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH,
    CREATE_TIME
FROM INFORMATION_SCHEMA.PARTITIONS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN ('market_quotes', 'market_depth', 'processing_log')
AND PARTITION_NAME IS NOT NULL
ORDER BY TABLE_NAME, PARTITION_NAME;

-- 创建存储过程：自动分区管理
DELIMITER //

-- 1. 创建新分区的存储过程
CREATE PROCEDURE CreateDailyPartitions(IN days_ahead INT)
BEGIN
    DECLARE i INT DEFAULT 0;
    DECLARE partition_date DATE;
    DECLARE next_date DATE;
    DECLARE partition_name VARCHAR(20);
    DECLARE sql_stmt TEXT;

    WHILE i <= days_ahead DO
        SET partition_date = DATE_ADD(CURDATE(), INTERVAL i DAY);
        SET next_date = DATE_ADD(partition_date, INTERVAL 1 DAY);
        SET partition_name = CONCAT('p', DATE_FORMAT(partition_date, '%Y%m%d'));

        -- 为 market_quotes 表创建分区
        SET sql_stmt = CONCAT(
            'ALTER TABLE market_quotes ADD PARTITION (',
            'PARTITION ', partition_name, ' VALUES LESS THAN (TO_DAYS(''', next_date, '''))',
            ')'
        );
        SET @sql = sql_stmt;
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        -- 为 market_depth 表创建分区
        SET sql_stmt = CONCAT(
            'ALTER TABLE market_depth ADD PARTITION (',
            'PARTITION ', partition_name, ' VALUES LESS THAN (TO_DAYS(''', next_date, '''))',
            ')'
        );
        SET @sql = sql_stmt;
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        -- 为 processing_log 表创建分区
        SET sql_stmt = CONCAT(
            'ALTER TABLE processing_log ADD PARTITION (',
            'PARTITION ', partition_name, ' VALUES LESS THAN (TO_DAYS(''', next_date, '''))',
            ')'
        );
        SET @sql = sql_stmt;
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;

        SET i = i + 1;
    END WHILE;
END //

-- 2. 清理历史分区的存储过程
CREATE PROCEDURE CleanHistoryPartitions(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE partition_name VARCHAR(64);
    DECLARE partition_date DATE;
    DECLARE cutoff_date DATE;
    DECLARE sql_stmt TEXT;

    -- 游标：获取所有分区信息
    DECLARE partition_cursor CURSOR FOR
        SELECT PARTITION_NAME
        FROM INFORMATION_SCHEMA.PARTITIONS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME IN ('market_quotes', 'market_depth', 'processing_log')
        AND PARTITION_NAME IS NOT NULL
        AND PARTITION_NAME != 'p_future'
        AND PARTITION_NAME REGEXP '^p[0-9]{8}$';

    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    SET cutoff_date = DATE_SUB(CURDATE(), INTERVAL days_to_keep DAY);

    OPEN partition_cursor;

    read_loop: LOOP
        FETCH partition_cursor INTO partition_name;
        IF done THEN
            LEAVE read_loop;
        END IF;

        -- 从分区名称解析日期 (格式: pYYYYMMDD)
        SET partition_date = STR_TO_DATE(SUBSTRING(partition_name, 2), '%Y%m%d');

        -- 如果分区日期早于保留期限，则删除分区
        IF partition_date < cutoff_date THEN
            -- 删除 market_quotes 分区
            SET sql_stmt = CONCAT('ALTER TABLE market_quotes DROP PARTITION ', partition_name);
            SET @sql = sql_stmt;
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            -- 删除 market_depth 分区
            SET sql_stmt = CONCAT('ALTER TABLE market_depth DROP PARTITION ', partition_name);
            SET @sql = sql_stmt;
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;

            -- 删除 processing_log 分区
            SET sql_stmt = CONCAT('ALTER TABLE processing_log DROP PARTITION ', partition_name);
            SET @sql = sql_stmt;
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
        END IF;
    END LOOP;

    CLOSE partition_cursor;

    -- 清理 system_metrics 表（非分区表）
    DELETE FROM system_metrics WHERE timestamp < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
END //

-- 3. 分区维护存储过程（创建未来分区 + 清理历史分区）
CREATE PROCEDURE MaintainPartitions(IN days_to_keep INT, IN days_ahead INT)
BEGIN
    -- 创建未来的分区
    CALL CreateDailyPartitions(days_ahead);

    -- 清理历史分区
    CALL CleanHistoryPartitions(days_to_keep);
END //

DELIMITER ;
