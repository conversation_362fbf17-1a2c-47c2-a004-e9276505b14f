-- ClickHouse数据库表结构设计
-- 针对高频金融数据优化的列式存储结构

-- 创建数据库
CREATE DATABASE IF NOT EXISTS market_data;
USE market_data;

-- 1. 股票基本信息表 (ReplacingMergeTree引擎，支持数据更新)
CREATE TABLE IF NOT EXISTS stock_info (
    symbol String,
    name String,
    market String,
    created_at DateTime DEFAULT now(),
    updated_at DateTime DEFAULT now(),
    _version UInt64 DEFAULT 1
) ENGINE = ReplacingMergeTree(_version)
ORDER BY symbol
SETTINGS index_granularity = 8192;

-- 2. 实时行情主表 (MergeTree引擎，按时间分区)
CREATE TABLE IF NOT EXISTS market_quotes (
    symbol String,
    time_dt DateTime,
    time UInt64,
    timetag String,
    last_price Nullable(Decimal64(3)),
    open_price Nullable(Decimal64(3)),
    high_price Nullable(Decimal64(3)),
    low_price Nullable(Decimal64(3)),
    last_close Nullable(Decimal64(3)),
    amount Nullable(UInt64),
    volume Nullable(UInt64),
    pvolume Nullable(UInt64),
    stock_status Nullable(Int32),
    open_int Nullable(Int32),
    settlement_price Nullable(Decimal64(3)),
    last_settlement_price Nullable(Decimal64(3)),
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(time_dt)
ORDER BY (symbol, time_dt)
TTL time_dt + INTERVAL 365 DAY
SETTINGS index_granularity = 8192;

-- 3. 买卖盘口数据表 (MergeTree引擎，按时间分区)
CREATE TABLE IF NOT EXISTS market_depth (
    symbol String,
    time_dt DateTime,
    time UInt64,
    timetag String,
    
    -- 卖盘价格 (ask_price)
    ask_price_1 Nullable(Decimal64(3)),
    ask_price_2 Nullable(Decimal64(3)),
    ask_price_3 Nullable(Decimal64(3)),
    ask_price_4 Nullable(Decimal64(3)),
    ask_price_5 Nullable(Decimal64(3)),
    
    -- 买盘价格 (bid_price)
    bid_price_1 Nullable(Decimal64(3)),
    bid_price_2 Nullable(Decimal64(3)),
    bid_price_3 Nullable(Decimal64(3)),
    bid_price_4 Nullable(Decimal64(3)),
    bid_price_5 Nullable(Decimal64(3)),
    
    -- 卖盘量 (ask_vol)
    ask_vol_1 Nullable(UInt64),
    ask_vol_2 Nullable(UInt64),
    ask_vol_3 Nullable(UInt64),
    ask_vol_4 Nullable(UInt64),
    ask_vol_5 Nullable(UInt64),
    
    -- 买盘量 (bid_vol)
    bid_vol_1 Nullable(UInt64),
    bid_vol_2 Nullable(UInt64),
    bid_vol_3 Nullable(UInt64),
    bid_vol_4 Nullable(UInt64),
    bid_vol_5 Nullable(UInt64),
    
    created_at DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(time_dt)
ORDER BY (symbol, time_dt)
TTL time_dt + INTERVAL 90 DAY
SETTINGS index_granularity = 8192;

-- 4. 数据处理日志表 (MergeTree引擎)
CREATE TABLE IF NOT EXISTS processing_log (
    message_id String,
    symbol String,
    processing_time DateTime,
    status Enum8('SUCCESS' = 1, 'FAILED' = 2, 'RETRY' = 3),
    error_message Nullable(String),
    raw_data Nullable(String)
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(processing_time)
ORDER BY (processing_time, symbol)
TTL processing_time + INTERVAL 30 DAY
SETTINGS index_granularity = 8192;

-- 5. 系统监控表 (MergeTree引擎)
CREATE TABLE IF NOT EXISTS system_metrics (
    metric_name String,
    metric_value Decimal64(6),
    metric_type Enum8('COUNTER' = 1, 'GAUGE' = 2, 'HISTOGRAM' = 3),
    labels String,
    timestamp DateTime DEFAULT now()
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(timestamp)
ORDER BY (metric_name, timestamp)
TTL timestamp + INTERVAL 90 DAY
SETTINGS index_granularity = 8192;

-- 插入一些测试数据
INSERT INTO stock_info (symbol, name, market) VALUES 
('600000.SH', '浦发银行', 'SH'),
('000001.SZ', '平安银行', 'SZ'),
('000002.SZ', '万科A', 'SZ');

-- 创建物化视图：最新行情视图
CREATE MATERIALIZED VIEW IF NOT EXISTS latest_quotes_mv
ENGINE = ReplacingMergeTree()
ORDER BY symbol
AS SELECT 
    symbol,
    argMax(time_dt, time_dt) as latest_time_dt,
    argMax(last_price, time_dt) as latest_price,
    argMax(open_price, time_dt) as open_price,
    argMax(high_price, time_dt) as high_price,
    argMax(low_price, time_dt) as low_price,
    argMax(last_close, time_dt) as last_close,
    argMax(amount, time_dt) as amount,
    argMax(volume, time_dt) as volume,
    argMax(created_at, time_dt) as created_at
FROM market_quotes
GROUP BY symbol;

-- 创建聚合表：分钟K线数据
CREATE TABLE IF NOT EXISTS market_quotes_1m (
    symbol String,
    time_minute DateTime,
    open_price Decimal64(3),
    high_price Decimal64(3),
    low_price Decimal64(3),
    close_price Decimal64(3),
    volume UInt64,
    amount UInt64,
    trade_count UInt32
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(time_minute)
ORDER BY (symbol, time_minute)
TTL time_minute + INTERVAL 180 DAY
SETTINGS index_granularity = 8192;

-- 创建物化视图：自动生成分钟K线
CREATE MATERIALIZED VIEW IF NOT EXISTS market_quotes_1m_mv
TO market_quotes_1m
AS SELECT 
    symbol,
    toStartOfMinute(time_dt) as time_minute,
    argMin(last_price, time_dt) as open_price,
    max(last_price) as high_price,
    min(last_price) as low_price,
    argMax(last_price, time_dt) as close_price,
    sum(volume) as volume,
    sum(amount) as amount,
    count() as trade_count
FROM market_quotes
WHERE last_price IS NOT NULL
GROUP BY symbol, time_minute;

-- 创建聚合表：小时K线数据
CREATE TABLE IF NOT EXISTS market_quotes_1h (
    symbol String,
    time_hour DateTime,
    open_price Decimal64(3),
    high_price Decimal64(3),
    low_price Decimal64(3),
    close_price Decimal64(3),
    volume UInt64,
    amount UInt64,
    trade_count UInt32
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(time_hour)
ORDER BY (symbol, time_hour)
TTL time_hour + INTERVAL 365 DAY
SETTINGS index_granularity = 8192;

-- 创建物化视图：自动生成小时K线
CREATE MATERIALIZED VIEW IF NOT EXISTS market_quotes_1h_mv
TO market_quotes_1h
AS SELECT 
    symbol,
    toStartOfHour(time_dt) as time_hour,
    argMin(last_price, time_dt) as open_price,
    max(last_price) as high_price,
    min(last_price) as low_price,
    argMax(last_price, time_dt) as close_price,
    sum(volume) as volume,
    sum(amount) as amount,
    count() as trade_count
FROM market_quotes
WHERE last_price IS NOT NULL
GROUP BY symbol, time_hour;

-- 创建字典：股票信息字典（用于快速查询）
CREATE DICTIONARY IF NOT EXISTS stock_info_dict (
    symbol String,
    name String,
    market String
)
PRIMARY KEY symbol
SOURCE(CLICKHOUSE(
    HOST 'localhost'
    PORT 9000
    USER 'default'
    PASSWORD ''
    DB 'market_data'
    TABLE 'stock_info'
))
LAYOUT(HASHED())
LIFETIME(MIN 300 MAX 600);

-- 创建函数：获取股票名称
CREATE FUNCTION IF NOT EXISTS getStockName AS (symbol) -> dictGet('stock_info_dict', 'name', symbol);

-- 创建函数：获取股票市场
CREATE FUNCTION IF NOT EXISTS getStockMarket AS (symbol) -> dictGet('stock_info_dict', 'market', symbol);

-- 优化设置
-- 设置合并参数以优化写入性能
ALTER TABLE market_quotes MODIFY SETTING max_parts_in_total = 10000;
ALTER TABLE market_depth MODIFY SETTING max_parts_in_total = 10000;
ALTER TABLE processing_log MODIFY SETTING max_parts_in_total = 5000;

-- 设置压缩算法
ALTER TABLE market_quotes MODIFY SETTING compression_codec = 'LZ4';
ALTER TABLE market_depth MODIFY SETTING compression_codec = 'LZ4';
ALTER TABLE processing_log MODIFY SETTING compression_codec = 'LZ4';

-- 创建索引以优化查询性能
-- 为symbol字段创建布隆过滤器索引
ALTER TABLE market_quotes ADD INDEX symbol_bloom symbol TYPE bloom_filter GRANULARITY 1;
ALTER TABLE market_depth ADD INDEX symbol_bloom symbol TYPE bloom_filter GRANULARITY 1;

-- 为时间字段创建MinMax索引
ALTER TABLE market_quotes ADD INDEX time_minmax time_dt TYPE minmax GRANULARITY 1;
ALTER TABLE market_depth ADD INDEX time_minmax time_dt TYPE minmax GRANULARITY 1;
