"""
Producer服务健康检查HTTP服务模块
提供服务健康状态检查接口
"""

import json
import logging
import threading
import time
import psutil
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from typing import Dict, Any
from urllib.parse import urlparse, parse_qs

from producer_metrics import get_producer_metrics_collector


class ProducerHealthCheckHandler(BaseHTTPRequestHandler):
    """Producer健康检查HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        self.logger = logging.getLogger(__name__)
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            query_params = parse_qs(parsed_path.query)
            
            if path == '/health':
                self._handle_health_check(query_params)
            elif path == '/health/live':
                self._handle_liveness_check()
            elif path == '/health/ready':
                self._handle_readiness_check()
            elif path == '/health/detailed':
                self._handle_detailed_health_check()
            elif path == '/stats':
                self._handle_stats()
            elif path == '/system':
                self._handle_system_info()
            elif path == '/metrics':
                self._handle_metrics_redirect()
            else:
                self._send_response(404, {'error': 'Not Found', 'path': path})
                
        except Exception as e:
            self.logger.error(f"处理健康检查请求时发生错误: {e}")
            self._send_response(500, {'error': 'Internal Server Error', 'message': str(e)})
    
    def _handle_health_check(self, query_params: Dict):
        """处理基本健康检查"""
        try:
            detail_level = query_params.get('detail', ['basic'])[0]
            
            if detail_level == 'detailed':
                response = self._get_detailed_health()
            else:
                response = self._get_basic_health()
            
            status_code = 200 if response['status'] == 'healthy' else 503
            self._send_response(status_code, response)
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            self._send_response(500, {'status': 'error', 'message': str(e)})
    
    def _handle_liveness_check(self):
        """处理存活性检查"""
        metrics_collector = get_producer_metrics_collector()
        
        response = {
            'status': 'alive',
            'timestamp': datetime.now().isoformat(),
            'uptime_seconds': time.time() - metrics_collector.start_time,
            'service': 'market_data_producer'
        }
        self._send_response(200, response)
    
    def _handle_readiness_check(self):
        """处理就绪性检查"""
        try:
            # 检查关键组件是否就绪
            system_health = self._get_system_health()
            
            # 检查系统资源是否充足
            cpu_ok = system_health['cpu_percent'] < 90
            memory_ok = system_health['memory_percent'] < 90
            
            # 整体就绪状态
            ready = cpu_ok and memory_ok
            
            response = {
                'status': 'ready' if ready else 'not_ready',
                'timestamp': datetime.now().isoformat(),
                'checks': {
                    'cpu': 'ok' if cpu_ok else 'high_usage',
                    'memory': 'ok' if memory_ok else 'high_usage'
                },
                'system': system_health
            }
            
            status_code = 200 if ready else 503
            self._send_response(status_code, response)
            
        except Exception as e:
            self.logger.error(f"就绪性检查失败: {e}")
            self._send_response(503, {'status': 'not_ready', 'error': str(e)})
    
    def _handle_detailed_health_check(self):
        """处理详细健康检查"""
        response = self._get_detailed_health()
        status_code = 200 if response['status'] == 'healthy' else 503
        self._send_response(status_code, response)
    
    def _handle_stats(self):
        """处理统计信息请求"""
        try:
            metrics_collector = get_producer_metrics_collector()
            
            stats = {
                'service_stats': {
                    'uptime_seconds': time.time() - metrics_collector.start_time,
                    'active_threads': threading.active_count()
                },
                'metrics_summary': metrics_collector.get_metrics_summary(),
                'timestamp': datetime.now().isoformat()
            }
            
            self._send_response(200, stats)
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            self._send_response(500, {'error': str(e)})
    
    def _handle_system_info(self):
        """处理系统信息请求"""
        try:
            system_info = self._get_system_info()
            self._send_response(200, system_info)
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            self._send_response(500, {'error': str(e)})
    
    def _handle_metrics_redirect(self):
        """处理Prometheus指标重定向"""
        metrics_port = get_producer_metrics_collector().port
        self._send_redirect(f"http://localhost:{metrics_port}/metrics")
    
    def _get_basic_health(self) -> Dict[str, Any]:
        """获取基本健康状态"""
        try:
            system_health = self._get_system_health()
            
            # 简单的健康判断：CPU和内存使用率不超过90%
            healthy = (system_health['cpu_percent'] < 90 and 
                      system_health['memory_percent'] < 90)
            
            return {
                'status': 'healthy' if healthy else 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0',
                'service': 'market_data_producer'
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _get_detailed_health(self) -> Dict[str, Any]:
        """获取详细健康状态"""
        try:
            metrics_collector = get_producer_metrics_collector()
            system_health = self._get_system_health()
            system_info = self._get_system_info()
            
            # 详细的健康判断
            cpu_healthy = system_health['cpu_percent'] < 90
            memory_healthy = system_health['memory_percent'] < 90
            disk_healthy = all(usage < 90 for usage in system_health.get('disk_usage', {}).values())
            
            overall_healthy = cpu_healthy and memory_healthy and disk_healthy
            
            return {
                'status': 'healthy' if overall_healthy else 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0',
                'service': 'market_data_producer',
                'uptime_seconds': time.time() - metrics_collector.start_time,
                'components': {
                    'system': {
                        'status': 'healthy' if overall_healthy else 'unhealthy',
                        'cpu': {
                            'status': 'healthy' if cpu_healthy else 'unhealthy',
                            'usage_percent': system_health['cpu_percent']
                        },
                        'memory': {
                            'status': 'healthy' if memory_healthy else 'unhealthy',
                            'usage_percent': system_health['memory_percent'],
                            'available_gb': system_health['memory_available_gb']
                        },
                        'disk': {
                            'status': 'healthy' if disk_healthy else 'unhealthy',
                            'usage': system_health.get('disk_usage', {})
                        }
                    },
                    'metrics': {
                        'status': 'healthy',
                        'port': metrics_collector.port
                    }
                },
                'system_info': system_info
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk_usage = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    usage_percent = (usage.used / usage.total) * 100
                    disk_usage[partition.device] = round(usage_percent, 2)
                except (PermissionError, OSError):
                    continue
            
            # 网络统计
            net_io = psutil.net_io_counters()
            
            return {
                'cpu_percent': round(cpu_percent, 2),
                'memory_percent': round(memory.percent, 2),
                'memory_available_gb': round(memory.available / (1024**3), 2),
                'memory_used_gb': round(memory.used / (1024**3), 2),
                'disk_usage': disk_usage,
                'network': {
                    'bytes_sent': net_io.bytes_sent if net_io else 0,
                    'bytes_recv': net_io.bytes_recv if net_io else 0
                } if net_io else None
            }
            
        except Exception as e:
            self.logger.error(f"获取系统健康状态失败: {e}")
            return {'error': str(e)}
    
    def _get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            import platform
            
            return {
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'python_version': platform.python_version(),
                'cpu_count': psutil.cpu_count(),
                'cpu_count_logical': psutil.cpu_count(logical=True),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return {'error': str(e)}
    
    def _send_response(self, status_code: int, data: Dict[str, Any]):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(data, indent=2, ensure_ascii=False)
        self.wfile.write(response_json.encode('utf-8'))
    
    def _send_redirect(self, location: str):
        """发送重定向响应"""
        self.send_response(302)
        self.send_header('Location', location)
        self.end_headers()
    
    def log_message(self, format, *args):
        """重写日志方法以使用我们的logger"""
        self.logger.info(f"{self.address_string()} - {format % args}")


class ProducerHealthCheckServer:
    """Producer健康检查HTTP服务器"""
    
    def __init__(self, host: str = '0.0.0.0', port: int = 8081):
        """
        初始化健康检查服务器
        
        Args:
            host: 监听主机
            port: 监听端口
        """
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.logger = logging.getLogger(__name__)
        
    def start(self):
        """启动健康检查服务器"""
        try:
            self.server = HTTPServer((self.host, self.port), ProducerHealthCheckHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            self.logger.info(f"Producer健康检查服务器已启动: http://{self.host}:{self.port}")
            self.logger.info("可用端点:")
            self.logger.info("  GET /health - 基本健康检查")
            self.logger.info("  GET /health/live - 存活性检查")
            self.logger.info("  GET /health/ready - 就绪性检查")
            self.logger.info("  GET /health/detailed - 详细健康检查")
            self.logger.info("  GET /stats - 统计信息")
            self.logger.info("  GET /system - 系统信息")
            self.logger.info("  GET /metrics - Prometheus指标重定向")
            
        except Exception as e:
            self.logger.error(f"启动Producer健康检查服务器失败: {e}")
            raise
    
    def stop(self):
        """停止健康检查服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.logger.info("Producer健康检查服务器已停止")
    
    def is_running(self) -> bool:
        """检查服务器是否在运行"""
        return self.server_thread and self.server_thread.is_alive()


# 全局健康检查服务器实例
_producer_health_server = None


def start_producer_health_server(host: str = '0.0.0.0', port: int = 8081) -> ProducerHealthCheckServer:
    """启动Producer健康检查服务器"""
    global _producer_health_server
    if _producer_health_server is None:
        _producer_health_server = ProducerHealthCheckServer(host, port)
        _producer_health_server.start()
    return _producer_health_server


def stop_producer_health_server():
    """停止Producer健康检查服务器"""
    global _producer_health_server
    if _producer_health_server:
        _producer_health_server.stop()
        _producer_health_server = None


def get_producer_health_server() -> ProducerHealthCheckServer:
    """获取Producer健康检查服务器实例"""
    global _producer_health_server
    return _producer_health_server
