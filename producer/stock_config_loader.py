"""
股票配置加载器
用于从配置文件加载股票代码列表
"""

import yaml
import logging
from typing import List, Dict, Any


class StockConfigLoader:
    """股票配置加载器类"""
    
    def __init__(self, config_path: str = "config/stocks_config.yaml"):
        """
        初始化股票配置加载器
        
        Args:
            config_path: 股票配置文件路径
        """
        self.config_path = config_path
        self.config = None
        self.logger = logging.getLogger(__name__)
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            self.logger.info(f"成功加载股票配置文件: {self.config_path}")
        except FileNotFoundError:
            self.logger.error(f"股票配置文件不存在: {self.config_path}")
            raise
        except yaml.YAMLError as e:
            self.logger.error(f"股票配置文件格式错误: {e}")
            raise
        except Exception as e:
            self.logger.error(f"加载股票配置文件失败: {e}")
            raise
    
    def get_stock_list(self, group_name: str = None) -> List[str]:
        """
        获取股票代码列表
        
        Args:
            group_name: 股票组名称，如果为None则使用配置中的active_group
            
        Returns:
            List[str]: 股票代码列表
        """
        if not self.config:
            raise ValueError("配置文件未加载")
        
        # 确定要使用的股票组
        if group_name is None:
            group_name = self.config.get('subscription', {}).get('active_group', 'default_list')
        
        # 特殊处理 "all" 组
        if group_name == 'all':
            return self._get_all_stocks()
        
        # 获取指定组的股票
        stocks_config = self.config.get('stocks', {})
        if group_name not in stocks_config:
            available_groups = list(stocks_config.keys())
            raise ValueError(f"股票组 '{group_name}' 不存在。可用组: {available_groups}")
        
        stock_list = stocks_config[group_name]
        
        # 应用过滤器
        filtered_list = self._apply_filters(stock_list)
        
        self.logger.info(f"加载股票组 '{group_name}': {len(filtered_list)} 只股票")
        return filtered_list
    
    def _get_all_stocks(self) -> List[str]:
        """获取所有股票组的股票"""
        all_stocks = []
        stocks_config = self.config.get('stocks', {})
        
        for group_name, stock_list in stocks_config.items():
            if isinstance(stock_list, list):
                all_stocks.extend(stock_list)
        
        # 去重
        unique_stocks = list(set(all_stocks))
        return self._apply_filters(unique_stocks)
    
    def _apply_filters(self, stock_list: List[str]) -> List[str]:
        """应用过滤器"""
        filters_config = self.config.get('filters', {})
        
        if not filters_config.get('enabled', False):
            return stock_list
        
        filtered_list = stock_list.copy()
        
        # 市场过滤
        allowed_markets = filters_config.get('markets', [])
        if allowed_markets:
            filtered_list = [
                stock for stock in filtered_list
                if any(stock.endswith(f'.{market}') for market in allowed_markets)
            ]
        
        # 代码前缀过滤
        allowed_prefixes = filters_config.get('code_prefixes', [])
        if allowed_prefixes:
            filtered_list = [
                stock for stock in filtered_list
                if any(stock.startswith(prefix) for prefix in allowed_prefixes)
            ]
        
        if len(filtered_list) != len(stock_list):
            self.logger.info(f"过滤器应用: {len(stock_list)} -> {len(filtered_list)} 只股票")
        
        return filtered_list
    
    def get_subscription_config(self) -> Dict[str, Any]:
        """获取订阅配置"""
        return self.config.get('subscription', {
            'period': '1m',
            'count': 0,
            'batch_size': 10,
            'delay_between_batches': 0.5,
            'delay_between_stocks': 0.1
        })
    
    def get_available_groups(self) -> List[str]:
        """获取可用的股票组列表"""
        if not self.config:
            return []
        
        stocks_config = self.config.get('stocks', {})
        groups = list(stocks_config.keys())
        groups.append('all')  # 添加特殊的 "all" 组
        return groups
    
    def reload_config(self):
        """重新加载配置文件"""
        self.logger.info("重新加载股票配置文件...")
        self._load_config()
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        if not self.config:
            return {}
        
        summary = {
            'available_groups': self.get_available_groups(),
            'active_group': self.config.get('subscription', {}).get('active_group', 'default_list'),
            'subscription_config': self.get_subscription_config(),
            'filters_enabled': self.config.get('filters', {}).get('enabled', False)
        }
        
        # 统计每个组的股票数量
        stocks_config = self.config.get('stocks', {})
        group_counts = {}
        for group_name, stock_list in stocks_config.items():
            if isinstance(stock_list, list):
                group_counts[group_name] = len(stock_list)
        
        summary['group_counts'] = group_counts
        summary['total_unique_stocks'] = len(self._get_all_stocks())
        
        return summary


# 全局股票配置加载器实例
_stock_config_loader = None


def get_stock_config_loader(config_path: str = "config/stocks_config.yaml") -> StockConfigLoader:
    """获取股票配置加载器单例"""
    global _stock_config_loader
    if _stock_config_loader is None:
        _stock_config_loader = StockConfigLoader(config_path)
    return _stock_config_loader


def load_stock_list(group_name: str = None, config_path: str = "config/stocks_config.yaml") -> List[str]:
    """
    便捷函数：加载股票代码列表
    
    Args:
        group_name: 股票组名称
        config_path: 配置文件路径
        
    Returns:
        List[str]: 股票代码列表
    """
    loader = get_stock_config_loader(config_path)
    return loader.get_stock_list(group_name)


def get_subscription_config(config_path: str = "config/stocks_config.yaml") -> Dict[str, Any]:
    """
    便捷函数：获取订阅配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict[str, Any]: 订阅配置
    """
    loader = get_stock_config_loader(config_path)
    return loader.get_subscription_config()


if __name__ == '__main__':
    # 测试代码
    try:
        loader = StockConfigLoader()
        
        print("=== 股票配置摘要 ===")
        summary = loader.get_config_summary()
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        print("\n=== 默认股票列表 ===")
        stocks = loader.get_stock_list()
        print(f"股票数量: {len(stocks)}")
        print("前10只股票:", stocks[:10])
        
        print("\n=== 所有股票 ===")
        all_stocks = loader.get_stock_list('all')
        print(f"总股票数量: {len(all_stocks)}")
        
    except Exception as e:
        print(f"测试失败: {e}")
