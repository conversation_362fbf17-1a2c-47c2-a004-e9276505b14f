#!/usr/bin/env python3
"""
测试Producer日志功能
验证修复后的日志编码是否正常工作
"""

import sys
import os
import logging
import time

def test_producer_logging():
    """测试Producer日志功能"""
    print("🧪 测试Producer日志功能...")
    
    try:
        # 导入main模块
        import main
        
        # 获取logger
        logger = logging.getLogger('producer_test')
        
        print("✅ Producer日志系统初始化成功")
        
        # 模拟Producer的各种日志场景
        test_scenarios = [
            ("INFO", "🚀 Producer服务启动"),
            ("INFO", "📊 从配置文件加载了 30 只股票"),
            ("INFO", "✅ 成功订阅股票: 600030.SH (中信证券)"),
            ("INFO", "✅ 成功订阅股票: 600000.SH (浦发银行)"),
            ("INFO", "📤 数据已发送到RabbitMQ: 1024 bytes, 耗时: 0.025s"),
            ("WARNING", "⚠️ RabbitMQ连接不稳定，正在重试..."),
            ("ERROR", "❌ 订阅股票 000001.SZ 失败: 网络超时"),
            ("INFO", "📈 监控指标更新: CPU 45.2%, 内存 67.8%"),
            ("INFO", "🔄 已处理 1000 条行情数据"),
            ("INFO", "💾 数据库连接池状态: 5/20 连接")
        ]
        
        print("\n📝 写入Producer测试日志...")
        for level, message in test_scenarios:
            if level == "INFO":
                logger.info(message)
            elif level == "WARNING":
                logger.warning(message)
            elif level == "ERROR":
                logger.error(message)
            
            time.sleep(0.1)  # 短暂延迟
        
        print("✅ Producer日志测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def check_log_output():
    """检查日志输出"""
    print("\n🔍 检查日志输出...")
    
    log_file = '../logs/producer.log'
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含测试内容
        test_keywords = [
            "Producer服务启动",
            "中信证券",
            "浦发银行",
            "RabbitMQ",
            "监控指标",
            "数据库连接池"
        ]
        
        found_keywords = []
        for keyword in test_keywords:
            if keyword in content:
                found_keywords.append(keyword)
        
        print(f"✅ 找到关键词: {len(found_keywords)}/{len(test_keywords)}")
        for keyword in found_keywords:
            print(f"   ✓ {keyword}")
        
        # 显示最新的日志行
        lines = content.strip().split('\n')
        if lines:
            print(f"\n📄 最新5行日志:")
            for line in lines[-5:]:
                if line.strip():
                    print(f"   {line}")
        
        return len(found_keywords) >= len(test_keywords) // 2
        
    except Exception as e:
        print(f"❌ 检查日志失败: {e}")
        return False


def test_emoji_and_chinese():
    """测试emoji和中文字符"""
    print("\n🧪 测试emoji和中文字符...")
    
    try:
        logger = logging.getLogger('emoji_test')
        
        # 测试各种字符
        test_chars = [
            "🚀🔧📊✅❌⚠️💾📈📤🔍",  # emoji
            "中文字符测试",
            "股票代码: 600000.SH",
            "数据处理: 成功",
            "错误信息: 连接失败",
            "Mixed: 🎉 成功处理 1000 条数据"
        ]
        
        for char_test in test_chars:
            logger.info(f"字符测试: {char_test}")
        
        print("✅ emoji和中文字符测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 字符测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始Producer日志功能测试")
    print("=" * 50)
    
    tests = [
        ("Producer日志功能", test_producer_logging),
        ("日志输出检查", check_log_output),
        ("emoji和中文测试", test_emoji_and_chinese)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 Producer日志功能测试全部通过！")
        print("\n📋 修复效果:")
        print("   ✅ 中文字符正常显示")
        print("   ✅ emoji表情正常显示")
        print("   ✅ 日志格式规范")
        print("   ✅ UTF-8编码正确")
        print("\n📝 日志文件:")
        log_path = os.path.abspath('../logs/producer.log')
        print(f"   {log_path}")
        print("\n🚀 Producer服务现在可以正常记录中文日志了！")
        return 0
    else:
        print("⚠️  部分测试失败")
        return 1


if __name__ == '__main__':
    exit(main())
