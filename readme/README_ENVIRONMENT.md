# 环境配置管理指南

## 🌍 环境概述

项目支持开发环境（dev）和生产环境（prod）的配置分离，通过启动参数来指定运行环境。

### 📁 目录结构

```
config/
├── dev/                    # 开发环境配置
│   ├── database.yaml       # 开发环境数据库配置
│   ├── rabbitmq_config.yaml # 开发环境RabbitMQ配置
│   └── stocks_config.yaml  # 开发环境股票配置
├── prod/                   # 生产环境配置
│   ├── database.yaml       # 生产环境数据库配置
│   ├── rabbitmq_config.yaml # 生产环境RabbitMQ配置
│   └── stocks_config.yaml  # 生产环境股票配置
├── database.yaml           # 当前使用的数据库配置
├── rabbitmq_config.yaml    # 当前使用的RabbitMQ配置
└── stocks_config.yaml      # 当前使用的股票配置
```

## 🔧 环境配置差异

### 开发环境 (dev)

#### 特点
- **数据库**: 本地MySQL/ClickHouse，较小连接池
- **队列**: 本地RabbitMQ，较小队列限制
- **股票**: 精选少量股票用于测试
- **日志**: DEBUG级别，详细日志
- **监控**: 标准端口 (8000, 8080, 8001, 8081)

#### 配置要点
```yaml
# 数据库配置
database: "market_data_dev"
pool:
  max_connections: 10
  
# RabbitMQ配置
virtual_host: "/dev"
queue:
  name: "market_data_queue_dev"
  x-max-length: 10000
  
# 股票配置
active_group: "test_group"  # 仅2-8只股票
```

### 生产环境 (prod)

#### 特点
- **数据库**: 生产集群，大连接池，启用SSL
- **队列**: RabbitMQ集群，大队列，高可用
- **股票**: 完整股票列表
- **日志**: INFO级别，结构化日志
- **监控**: 生产端口 (9090, 9080, 9091, 9081)

#### 配置要点
```yaml
# 数据库配置
database: "market_data_prod"
pool:
  max_connections: 50
ssl:
  enabled: true
  
# RabbitMQ配置
virtual_host: "/prod"
queue:
  name: "market_data_queue_prod"
  x-max-length: 100000
  
# 股票配置
active_group: "default_list"  # 完整股票列表
```

## 🚀 使用方法

### 1. 快速启动

#### 开发环境
```bash
# Windows
scripts\start_dev.bat

# 或手动启动
python producer/main.py --env dev
python consumer/main.py --env dev
python consumer/threaded_main.py --env dev
```

#### 生产环境
```bash
# Windows
scripts\start_prod.bat

# 或手动启动
python producer/main.py --env prod
python consumer/main.py --env prod
python consumer/threaded_main.py --env prod
```

### 2. 命令行参数

#### Producer
```bash
python producer/main.py \
  --env dev \                          # 环境选择
  --rabbitmq-config config/dev/rabbitmq_config.yaml \  # 可选
  --stocks-config config/dev/stocks_config.yaml \      # 可选
  --log-level DEBUG
```

#### Consumer
```bash
python consumer/main.py \
  --env dev \                          # 环境选择
  --config config/dev/rabbitmq_config.yaml \          # 可选
  --db-config config/dev/database.yaml \              # 可选
  --metrics-port 8000 \                # 可选
  --health-port 8080 \                 # 可选
  --log-level DEBUG
```

#### 多线程Consumer
```bash
python consumer/threaded_main.py \
  --env dev \                          # 环境选择
  --config config/dev/rabbitmq_config.yaml \          # 可选
  --db-config config/dev/database.yaml \              # 可选
  --metrics-port 8001 \                # 可选
  --health-port 8081 \                 # 可选
  --log-level DEBUG
```

### 3. 环境切换工具

```bash
# 查看当前环境信息
python scripts/switch_env.py info

# 验证环境配置
python scripts/switch_env.py validate --env dev
python scripts/switch_env.py validate --env prod

# 切换环境
python scripts/switch_env.py switch --env dev
python scripts/switch_env.py switch --env prod
```

## 🔧 环境配置

### 1. 开发环境设置

#### 环境变量
```bash
# Windows
set MYSQL_PASSWORD=123456
set RABBITMQ_PASSWORD=admin123

# Linux/Mac
export MYSQL_PASSWORD=123456
export RABBITMQ_PASSWORD=admin123
```

#### 服务配置
- **MySQL**: 本地安装，端口3306
- **RabbitMQ**: 本地安装，端口5672
- **ClickHouse**: 本地安装，端口9000（可选）

### 2. 生产环境设置

#### 环境变量
```bash
# 必需环境变量
export MYSQL_PASSWORD=your_secure_password
export RABBITMQ_PASSWORD=your_secure_password
export CLICKHOUSE_PASSWORD=your_secure_password

# 可选环境变量
export INSTANCE_ID=prod-001
export DEPLOYMENT_ID=deploy-001
export CLUSTER_ID=cluster-001
```

#### 服务配置
- **MySQL**: 集群部署，主从复制
- **RabbitMQ**: 集群部署，高可用
- **ClickHouse**: 集群部署（可选）

## 📊 监控端点

### 开发环境
| 服务 | 健康检查 | 指标 |
|------|----------|------|
| Producer | http://localhost:8081/health | http://localhost:8001/metrics |
| Consumer | http://localhost:8080/health | http://localhost:8000/metrics |
| 多线程Consumer | http://localhost:8081/health | http://localhost:8001/metrics |

### 生产环境
| 服务 | 健康检查 | 指标 |
|------|----------|------|
| Producer | http://localhost:8081/health | http://localhost:8001/metrics |
| Consumer | http://localhost:9080/health | http://localhost:9090/metrics |
| 多线程Consumer | http://localhost:9081/health | http://localhost:9091/metrics |

## 🔍 故障排除

### 常见问题

#### 1. 配置文件不存在
```bash
❌ 数据库配置文件不存在: config/dev/database.yaml
```
**解决方案**: 确保已创建对应环境的配置文件

#### 2. 环境变量未设置
```bash
❌ 环境变量 MYSQL_PASSWORD 未设置
```
**解决方案**: 设置必需的环境变量

#### 3. 端口冲突
```bash
❌ 端口 8080 已被占用
```
**解决方案**: 使用 `--metrics-port` 和 `--health-port` 参数指定其他端口

#### 4. 数据库连接失败
```bash
❌ 数据库连接不健康
```
**解决方案**: 
- 检查数据库服务是否运行
- 验证连接参数
- 检查网络连接

### 调试命令

```bash
# 检查环境配置
python scripts/switch_env.py info

# 验证配置文件
python scripts/switch_env.py validate --env dev

# 测试数据库连接
python scripts/test_database_stats.py

# 测试多线程消费者
python scripts/test_threaded_consumer.py
```

## 📋 最佳实践

### 1. 开发环境
- 使用较小的数据集进行测试
- 启用详细日志便于调试
- 定期清理测试数据
- 使用本地服务避免影响生产

### 2. 生产环境
- 设置适当的资源限制
- 启用监控和告警
- 定期备份配置和数据
- 使用环境变量管理敏感信息

### 3. 配置管理
- 版本控制配置文件
- 定期验证配置有效性
- 文档化配置变更
- 测试环境切换流程

## 🎯 总结

通过环境配置分离，我们实现了：

✅ **配置隔离**: 开发和生产环境完全分离  
✅ **灵活切换**: 通过参数轻松切换环境  
✅ **安全管理**: 生产环境使用环境变量管理敏感信息  
✅ **便捷部署**: 自动化脚本简化部署流程  
✅ **监控分离**: 不同环境使用不同监控端点  

这种设计确保了开发和生产环境的独立性，提高了系统的可维护性和安全性。
