# 数据库分区管理文档

## 概述

为了提高查询性能和便于数据管理，系统对以下三个核心表实施了按天分区策略：

- **market_quotes** - 实时行情主表
- **market_depth** - 买卖盘口数据表  
- **processing_log** - 数据处理日志表

每个表按照 `created_at` 或 `processing_time` 字段进行日分区，每天一个分区。

## 分区策略

### 分区命名规则
- 格式：`pYYYYMMDD`
- 示例：`p20250623` (2025年6月23日的分区)
- 特殊分区：`p_future` (用于存储未来数据的默认分区)

### 分区字段
- **market_quotes**: 按 `created_at` 字段分区
- **market_depth**: 按 `created_at` 字段分区
- **processing_log**: 按 `processing_time` 字段分区

### 主键调整
由于MySQL分区表的限制，主键必须包含分区字段：
- **原主键**: `id`
- **新主键**: `(id, created_at)` 或 `(id, processing_time)`

## 自动分区管理

### 事件调度器
系统配置了自动分区维护事件，每天凌晨2点执行：
```sql
-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- 自动维护事件
CREATE EVENT auto_partition_maintenance
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '02:00:00')
DO
BEGIN
    -- 保留30天数据，提前创建7天分区
    CALL MaintainPartitions(30, 7);
END;
```

### 存储过程

#### 1. CreateDailyPartitions(days_ahead)
创建未来几天的分区
```sql
CALL CreateDailyPartitions(7);  -- 创建未来7天的分区
```

#### 2. CleanHistoryPartitions(days_to_keep)
清理历史分区
```sql
CALL CleanHistoryPartitions(30);  -- 删除30天前的分区
```

#### 3. MaintainPartitions(days_to_keep, days_ahead)
综合维护分区
```sql
CALL MaintainPartitions(30, 7);  -- 保留30天，提前创建7天
```

## 手动分区管理

### 使用Python脚本

#### 安装和配置
```bash
# 进入项目目录
cd /path/to/your/project

# 确保Python环境正确
conda activate qmt
```

#### 基本用法

**查看分区状态**
```bash
python scripts/partition_manager.py status
```

**创建未来分区**
```bash
python scripts/partition_manager.py create --days 7
```

**清理历史分区**
```bash
python scripts/partition_manager.py clean --keep 30
```

**维护分区（推荐）**
```bash
python scripts/partition_manager.py maintain --keep 30 --ahead 7
```

#### 命令参数说明

| 命令 | 参数 | 说明 | 默认值 |
|------|------|------|--------|
| `create` | `--days` | 提前创建多少天的分区 | 7 |
| `clean` | `--keep` | 保留多少天的数据 | 30 |
| `maintain` | `--keep` | 保留多少天的数据 | 30 |
| `maintain` | `--ahead` | 提前创建多少天的分区 | 7 |
| 所有命令 | `--config` | 数据库配置文件路径 | config/database.yaml |

### 使用SQL命令

**查看分区信息**
```sql
-- 查看分区状态
SELECT * FROM partition_status;

-- 查看具体表的分区
SELECT 
    PARTITION_NAME,
    PARTITION_DESCRIPTION,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH
FROM INFORMATION_SCHEMA.PARTITIONS
WHERE TABLE_SCHEMA = 'market_data'
AND TABLE_NAME = 'market_quotes'
AND PARTITION_NAME IS NOT NULL;
```

**手动创建分区**
```sql
-- 为明天创建分区
ALTER TABLE market_quotes ADD PARTITION (
    PARTITION p20250624 VALUES LESS THAN (TO_DAYS('2025-06-25'))
);
```

**手动删除分区**
```sql
-- 删除指定日期的分区
ALTER TABLE market_quotes DROP PARTITION p20250601;
```

## 性能优化

### 查询优化
分区表查询时，尽量在WHERE条件中包含分区字段：

**推荐的查询方式**
```sql
-- 查询特定日期的数据
SELECT * FROM market_quotes 
WHERE created_at >= '2025-06-23 00:00:00' 
AND created_at < '2025-06-24 00:00:00'
AND symbol = '600000.SH';

-- 查询最近几天的数据
SELECT * FROM market_quotes 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 3 DAY)
AND symbol = '600000.SH';
```

**避免的查询方式**
```sql
-- 不包含分区字段的查询会扫描所有分区
SELECT * FROM market_quotes WHERE symbol = '600000.SH';
```

### 索引策略
- 保持原有的业务索引
- 分区字段会自动参与索引优化
- 复合索引中建议将分区字段放在后面

## 监控和维护

### 分区状态监控
```sql
-- 查看各分区的数据量
SELECT 
    TABLE_NAME,
    PARTITION_NAME,
    TABLE_ROWS,
    ROUND(DATA_LENGTH/1024/1024, 2) AS DATA_MB,
    ROUND(INDEX_LENGTH/1024/1024, 2) AS INDEX_MB
FROM partition_status
ORDER BY TABLE_NAME, PARTITION_NAME;
```

### 定期维护任务
建议设置以下定期维护任务：

1. **每日检查** - 确认新分区创建成功
2. **每周检查** - 验证分区数据分布
3. **每月检查** - 评估分区策略效果

### 故障排除

**常见问题及解决方案**

1. **分区创建失败**
   - 检查事件调度器是否启用：`SHOW VARIABLES LIKE 'event_scheduler';`
   - 检查存储过程是否存在：`SHOW PROCEDURE STATUS;`

2. **查询性能下降**
   - 确认查询条件包含分区字段
   - 检查分区剪枝是否生效：`EXPLAIN PARTITIONS SELECT ...`

3. **分区过多**
   - 调整保留天数参数
   - 手动清理不需要的历史分区

## 最佳实践

1. **数据保留策略**
   - 生产环境建议保留30-90天数据
   - 测试环境可以保留7-30天数据

2. **分区维护**
   - 使用自动化脚本进行分区维护
   - 定期监控分区状态和性能

3. **备份策略**
   - 可以按分区进行备份
   - 历史分区可以备份到冷存储

4. **应用程序适配**
   - 查询时尽量包含时间范围条件
   - 避免跨大量分区的查询操作
