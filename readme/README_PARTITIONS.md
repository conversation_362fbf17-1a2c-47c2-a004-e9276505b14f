# 数据库分区表快速使用指南

## 🚀 快速开始

### 1. 重新创建数据库表结构

```bash
# 备份现有数据（如果有重要数据）
mysqldump -u root -p market_data > backup_$(date +%Y%m%d_%H%M%S).sql

# 删除现有数据库（谨慎操作！）
mysql -u root -p -e "DROP DATABASE IF EXISTS market_data;"

# 重新创建数据库和分区表
mysql -u root -p < config/database_schema.sql
```

### 2. 检查分区系统状态

```bash
# 进入项目目录
cd /path/to/your/project

# 激活conda环境
conda activate qmt

# 运行分区系统检查
python scripts/init_partitions.py
```

### 3. 查看分区状态

```bash
# 查看分区详细信息
python scripts/partition_manager.py status
```

## 📊 分区表概览

### 修改的表

| 表名 | 分区字段 | 分区策略 | 主键变更 |
|------|----------|----------|----------|
| `market_quotes` | `created_at` | 按天分区 | `(id, created_at)` |
| `market_depth` | `created_at` | 按天分区 | `(id, created_at)` |
| `processing_log` | `processing_time` | 按天分区 | `(id, processing_time)` |

### 主要变更

1. **主键调整**：所有分区表的主键都包含了分区字段
2. **唯一键调整**：`uk_symbol_time` 改为 `(symbol, time, created_at)`
3. **分区命名**：格式为 `pYYYYMMDD`，如 `p20250623`
4. **默认分区**：`p_future` 用于存储未来数据

## 🔧 日常维护

### 自动维护（推荐）

系统已配置自动分区维护，每天凌晨2点执行：
- 保留30天数据
- 提前创建7天分区
- 自动清理过期分区

### 手动维护

```bash
# 维护分区（保留30天，提前创建7天）
python scripts/partition_manager.py maintain --keep 30 --ahead 7

# 只创建未来分区
python scripts/partition_manager.py create --days 7

# 只清理历史分区
python scripts/partition_manager.py clean --keep 30
```

## 📈 性能优化建议

### 查询优化

**✅ 推荐的查询方式**
```sql
-- 查询特定日期范围的数据
SELECT * FROM market_quotes 
WHERE created_at >= '2025-06-23 00:00:00' 
AND created_at < '2025-06-24 00:00:00'
AND symbol = '600000.SH';

-- 查询最近几天的数据
SELECT * FROM market_quotes 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 3 DAY)
AND symbol = '600000.SH';
```

**❌ 避免的查询方式**
```sql
-- 不包含分区字段的查询会扫描所有分区
SELECT * FROM market_quotes WHERE symbol = '600000.SH';
```

### 应用程序适配

由于主键变更，应用程序可能需要适配：

1. **插入操作**：无需修改，`created_at` 有默认值
2. **更新操作**：WHERE条件中建议包含分区字段
3. **删除操作**：WHERE条件中建议包含分区字段

## 🔍 监控和故障排除

### 检查分区状态

```sql
-- 查看分区信息
SELECT * FROM partition_status;

-- 查看事件调度器状态
SHOW VARIABLES LIKE 'event_scheduler';

-- 查看自动维护事件
SELECT * FROM INFORMATION_SCHEMA.EVENTS 
WHERE EVENT_NAME = 'auto_partition_maintenance';
```

### 常见问题

**1. 分区创建失败**
```bash
# 检查存储过程是否存在
mysql -u root -p -e "SHOW PROCEDURE STATUS WHERE Name LIKE '%Partition%';"

# 手动创建分区
python scripts/partition_manager.py create --days 1
```

**2. 查询性能下降**
```sql
-- 检查查询是否使用了分区剪枝
EXPLAIN PARTITIONS SELECT * FROM market_quotes 
WHERE created_at >= '2025-06-23' AND symbol = '600000.SH';
```

**3. 事件调度器未启用**
```sql
-- 启用事件调度器
SET GLOBAL event_scheduler = ON;
```

## 📋 迁移检查清单

- [ ] 备份现有数据
- [ ] 重新创建数据库表结构
- [ ] 运行分区系统检查
- [ ] 验证分区创建成功
- [ ] 测试数据插入和查询
- [ ] 检查事件调度器状态
- [ ] 验证自动维护事件
- [ ] 更新应用程序查询（如需要）
- [ ] 监控系统性能

## 🆘 紧急恢复

如果分区表出现问题，可以快速恢复到非分区表：

```sql
-- 创建临时表
CREATE TABLE market_quotes_backup AS SELECT * FROM market_quotes;

-- 删除分区表
DROP TABLE market_quotes;

-- 重新创建非分区表
CREATE TABLE market_quotes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    -- ... 其他字段
    UNIQUE KEY uk_symbol_time (symbol, time)
);

-- 恢复数据
INSERT INTO market_quotes SELECT * FROM market_quotes_backup;
```

## 📞 技术支持

如遇到问题，请检查：
1. 日志文件：`logs/consumer.log`
2. 分区状态：`python scripts/partition_manager.py status`
3. 数据库错误日志
4. 事件调度器状态

更多详细信息请参考：`docs/partition_management.md`
