# 单线程消费者使用指南

## 🚀 概述

单线程消费者是一个简化版本的市场数据消费者，去掉了多线程的复杂性，专注于稳定性和简单性。适合对性能要求不高但需要高稳定性的场景。

## ✨ 主要特性

### 🔧 **核心功能**
- ✅ **单线程处理**: 简化架构，避免线程同步问题
- ✅ **批量处理**: 可选的批量处理提高性能
- ✅ **自动重连**: 网络中断自动恢复
- ✅ **健康监控**: 完整的健康检查和指标监控
- ✅ **多数据库支持**: 支持MySQL和ClickHouse

### 🛡️ **稳定性特性**
- ✅ **连接稳定性**: TCP keepalive + 心跳机制
- ✅ **错误恢复**: 智能错误分类和处理
- ✅ **优雅关闭**: 信号处理和资源清理
- ✅ **日志记录**: 详细的操作日志

## 📊 性能对比

| 特性 | 单线程消费者 | 多线程消费者 | 说明 |
|------|-------------|-------------|------|
| **处理速度** | 50-200 msg/s | 500-2000 msg/s | 单线程性能较低但稳定 |
| **内存使用** | 低 | 中-高 | 单线程内存占用更少 |
| **CPU使用** | 低 | 中-高 | 单核心使用 |
| **稳定性** | 高 | 中 | 无线程同步问题 |
| **复杂度** | 低 | 高 | 架构简单易维护 |
| **适用场景** | 小规模数据 | 大规模数据 | 根据数据量选择 |

## ⚙️ 配置说明

### 基础配置

```yaml
# config/dev/rabbitmq_config.yaml
consumer:
  # 单线程批量处理配置
  batch:
    enabled: true              # 启用批量处理
    size: 50                   # 批量大小
    timeout_seconds: 3         # 批量超时（秒）
  
  # 性能配置
  performance:
    prefetch_count: 5          # 预取数量（单线程保守设置）
    enable_batch_insert: true  # 启用批量插入
```

### 连接配置

```yaml
rabbitmq:
  connection:
    heartbeat: 300             # 5分钟心跳
    socket_timeout: 30         # 30秒socket超时
    connection_attempts: 3     # 连接尝试次数
    retry_delay: 2            # 重试延迟
    
    # TCP keepalive配置
    tcp_options:
      TCP_KEEPIDLE: 600        # 10分钟空闲检测
      TCP_KEEPINTVL: 30        # 30秒检测间隔
      TCP_KEEPCNT: 3           # 3次失败断开
```

## 🚀 使用方法

### 1. **启动单线程消费者**

```bash
# 开发环境
python consumer/single_thread_main.py --env dev

# 生产环境
python consumer/single_thread_main.py --env prod

# 自定义端口
python consumer/single_thread_main.py --env dev --metrics-port 8001 --health-port 8081

# 调试模式
python consumer/single_thread_main.py --env dev --log-level DEBUG
```

### 2. **监控和检查**

```bash
# 健康检查
curl http://localhost:8081/health

# 获取指标
curl http://localhost:8001/metrics

# 查看日志
tail -f logs/single_thread_consumer.log
```

### 3. **性能测试**

```bash
# 运行性能测试
python scripts/test_single_thread_consumer.py

# 测试将运行3分钟并生成详细报告
```

## 📈 性能优化

### 1. **批量处理优化**

```yaml
# 高吞吐量配置
consumer:
  batch:
    enabled: true
    size: 100                  # 增加批量大小
    timeout_seconds: 1         # 减少超时时间

# 低延迟配置
consumer:
  batch:
    enabled: false             # 禁用批量处理
    # 或者
    size: 10                   # 小批量
    timeout_seconds: 0.5       # 短超时
```

### 2. **预取优化**

```yaml
rabbitmq:
  connection:
    # 高性能配置
    prefetch_count: 10         # 增加预取数量
    
    # 稳定性配置
    prefetch_count: 3          # 保守预取数量
```

### 3. **数据库优化**

```yaml
# ClickHouse批量插入
clickhouse:
  batch:
    size: 1000                 # 大批量插入
    compression: true          # 启用压缩
    
# MySQL优化
mysql:
  pool:
    max_connections: 5         # 适中连接池
```

## 🔍 监控指标

### 关键指标

```bash
# 消息处理指标
messages_received_total        # 接收消息总数
messages_processed_total       # 处理消息总数
messages_failed_total          # 失败消息总数

# 连接状态指标
rabbitmq_connection_status     # RabbitMQ连接状态
database_connection_status     # 数据库连接状态

# 性能指标
message_processing_duration    # 消息处理时长
database_operation_duration    # 数据库操作时长
```

### 告警阈值

```yaml
# 建议的告警阈值
alerts:
  message_processing_rate_low: 10    # 处理速率低于10 msg/s
  error_rate_high: 5                 # 错误率高于5%
  connection_down: 1                 # 连接断开
  memory_usage_high: 80              # 内存使用率高于80%
```

## 🛠️ 故障排除

### 常见问题

#### 1. **处理速度慢**
```
症状: 消息处理速率低于预期
```
**解决方案**:
- 启用批量处理
- 增加批量大小
- 检查数据库性能
- 优化网络连接

#### 2. **连接频繁断开**
```
症状: RabbitMQ连接不稳定
```
**解决方案**:
- 检查网络稳定性
- 调整心跳间隔
- 启用TCP keepalive
- 检查防火墙设置

#### 3. **内存使用过高**
```
症状: 内存占用持续增长
```
**解决方案**:
- 减少批量大小
- 降低预取数量
- 检查内存泄漏
- 重启消费者

#### 4. **消息处理失败**
```
症状: 大量消息处理失败
```
**解决方案**:
- 检查数据格式
- 验证数据库连接
- 查看错误日志
- 检查权限设置

### 调试技巧

#### 1. **启用详细日志**
```bash
python consumer/single_thread_main.py --env dev --log-level DEBUG
```

#### 2. **监控实时指标**
```bash
# 实时查看指标
watch -n 5 'curl -s http://localhost:8001/metrics | grep messages'

# 实时查看健康状态
watch -n 10 'curl -s http://localhost:8081/health | jq .'
```

#### 3. **分析日志**
```bash
# 查看错误日志
grep ERROR logs/single_thread_consumer.log

# 查看性能统计
grep "消息处理完成" logs/single_thread_consumer.log | tail -20
```

## 📋 最佳实践

### 1. **部署建议**
- 使用systemd管理服务
- 配置日志轮转
- 设置监控告警
- 定期备份配置

### 2. **性能调优**
- 根据数据量调整批量大小
- 监控系统资源使用
- 定期分析性能指标
- 优化数据库查询

### 3. **运维管理**
- 定期检查日志
- 监控连接状态
- 备份重要数据
- 制定故障恢复计划

## 🎯 总结

### 适用场景

✅ **推荐使用**:
- 小到中等规模数据处理
- 对稳定性要求高的场景
- 资源受限的环境
- 简单的数据处理逻辑

❌ **不推荐使用**:
- 大规模高并发数据处理
- 对性能要求极高的场景
- 复杂的数据处理逻辑
- 需要实时处理的场景

### 核心优势

🟢 **简单稳定**: 架构简单，故障点少  
🟢 **资源友好**: 内存和CPU使用较低  
🟢 **易于维护**: 代码简洁，易于调试  
🟢 **自动恢复**: 具备完善的错误恢复机制  

通过合理配置和优化，单线程消费者可以在保证稳定性的前提下，提供足够的数据处理能力！
