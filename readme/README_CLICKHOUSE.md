# ClickHouse集成使用指南

## 🚀 快速开始

### 1. 安装依赖

```bash
# 激活conda环境
conda activate qmt

# 安装ClickHouse驱动
pip install clickhouse-driver
```

### 2. 安装和启动ClickHouse服务器

#### Windows
```bash
# 下载并安装ClickHouse
# 访问: https://clickhouse.com/docs/en/getting-started/install

# 或使用Docker
docker run -d --name clickhouse-server --ulimit nofile=262144:262144 -p 9000:9000 -p 8123:8123 clickhouse/clickhouse-server
```

#### Linux/macOS
```bash
# 使用包管理器安装
curl https://clickhouse.com/ | sh
sudo ./clickhouse install

# 启动服务
sudo clickhouse start

# 或使用Docker
docker run -d --name clickhouse-server --ulimit nofile=262144:262144 -p 9000:9000 -p 8123:8123 clickhouse/clickhouse-server
```

### 3. 初始化ClickHouse数据库

```bash
# 初始化ClickHouse表结构
python scripts/init_clickhouse.py

# 检查初始化状态
python scripts/init_clickhouse.py --check-only
```

### 4. 配置数据库类型

编辑 `config/database.yaml`：

```yaml
# 使用ClickHouse作为主数据库
database_type: clickhouse

# 或启用双写模式（MySQL + ClickHouse）
dual_write:
  enabled: true
  primary: mysql
  secondary: clickhouse
  fail_on_secondary_error: false
```

## 📊 功能特性

### 支持的存储模式

| 模式 | 说明 | 配置 |
|------|------|------|
| **MySQL单库** | 仅使用MySQL | `database_type: mysql` |
| **ClickHouse单库** | 仅使用ClickHouse | `database_type: clickhouse` |
| **双写模式** | 同时写入两个数据库 | `dual_write.enabled: true` |

### ClickHouse优势

1. **列式存储**：针对分析查询优化，压缩率高
2. **高性能写入**：支持批量写入，吞吐量大
3. **实时分析**：支持复杂的聚合查询
4. **自动分区**：按月分区，自动TTL管理
5. **物化视图**：自动生成K线数据

### 表结构设计

#### 主要数据表

| 表名 | 引擎 | 分区策略 | TTL | 说明 |
|------|------|----------|-----|------|
| `market_quotes` | MergeTree | 按月分区 | 365天 | 实时行情数据 |
| `market_depth` | MergeTree | 按月分区 | 90天 | 买卖盘口数据 |
| `processing_log` | MergeTree | 按月分区 | 30天 | 处理日志 |

#### 聚合表（自动生成）

| 表名 | 说明 | 数据来源 |
|------|------|----------|
| `market_quotes_1m` | 分钟K线 | 自动聚合 |
| `market_quotes_1h` | 小时K线 | 自动聚合 |
| `latest_quotes_mv` | 最新行情 | 物化视图 |

## 🔧 使用方法

### 1. 测试ClickHouse功能

```bash
# 运行完整功能测试
python scripts/test_clickhouse.py

# 测试特定功能
python scripts/test_clickhouse.py --test connection
python scripts/test_clickhouse.py --test insert
python scripts/test_clickhouse.py --test batch
```

### 2. 启动消费者服务

```bash
# 使用ClickHouse存储
python consumer/main.py

# 消费者会自动根据配置选择存储后端
```

### 3. 查询数据

#### 基本查询
```sql
-- 查询最新行情
SELECT * FROM latest_quotes_mv WHERE symbol = '600000.SH';

-- 查询历史数据
SELECT * FROM market_quotes 
WHERE symbol = '600000.SH' 
AND time_dt >= '2025-06-23 09:30:00'
ORDER BY time_dt DESC
LIMIT 100;
```

#### 聚合查询
```sql
-- 查询分钟K线
SELECT * FROM market_quotes_1m 
WHERE symbol = '600000.SH' 
AND time_minute >= '2025-06-23 09:30:00'
ORDER BY time_minute;

-- 计算成交量统计
SELECT 
    symbol,
    sum(volume) as total_volume,
    avg(last_price) as avg_price,
    max(high_price) as max_price,
    min(low_price) as min_price
FROM market_quotes 
WHERE time_dt >= today()
GROUP BY symbol
ORDER BY total_volume DESC;
```

### 4. 监控和维护

#### 查看表状态
```sql
-- 查看表大小和行数
SELECT 
    table,
    formatReadableSize(total_bytes) as size,
    total_rows
FROM system.tables 
WHERE database = 'market_data'
ORDER BY total_bytes DESC;

-- 查看分区信息
SELECT 
    table,
    partition,
    rows,
    formatReadableSize(bytes_on_disk) as size
FROM system.parts 
WHERE database = 'market_data'
ORDER BY table, partition;
```

#### 性能监控
```sql
-- 查看查询性能
SELECT 
    query,
    query_duration_ms,
    read_rows,
    read_bytes
FROM system.query_log 
WHERE event_time >= now() - INTERVAL 1 HOUR
ORDER BY query_duration_ms DESC
LIMIT 10;
```

## ⚙️ 配置说明

### ClickHouse配置项

```yaml
clickhouse:
  primary:
    host: "127.0.0.1"          # ClickHouse服务器地址
    port: 9000                 # 原生协议端口
    username: "default"        # 用户名
    password: ""               # 密码
    database: "market_data"    # 数据库名
    compression: true          # 启用压缩
    
  pool:
    min_connections: 3         # 最小连接数
    max_connections: 10        # 最大连接数
    
  batch:
    size: 10000               # 批量大小
    timeout: 30               # 批量超时
    
  ttl:
    market_quotes_days: 365   # 行情数据保留天数
    market_depth_days: 90     # 盘口数据保留天数
    processing_log_days: 30   # 日志保留天数
```

### 双写模式配置

```yaml
dual_write:
  enabled: true                    # 启用双写
  primary: mysql                   # 主数据库
  secondary: clickhouse            # 辅助数据库
  fail_on_secondary_error: false   # 辅助库失败时是否整体失败
```

## 🔍 故障排除

### 常见问题

**1. 连接失败**
```bash
# 检查ClickHouse服务状态
sudo systemctl status clickhouse-server

# 检查端口是否开放
netstat -tlnp | grep 9000

# 测试连接
clickhouse-client --query "SELECT 1"
```

**2. 权限问题**
```sql
-- 创建用户和授权
CREATE USER market_user IDENTIFIED BY 'password';
GRANT ALL ON market_data.* TO market_user;
```

**3. 性能问题**
```sql
-- 检查表优化状态
OPTIMIZE TABLE market_quotes FINAL;

-- 查看慢查询
SELECT * FROM system.query_log 
WHERE query_duration_ms > 1000 
ORDER BY event_time DESC;
```

**4. 存储空间**
```sql
-- 清理旧数据
ALTER TABLE market_quotes DELETE WHERE time_dt < now() - INTERVAL 30 DAY;

-- 手动触发TTL清理
ALTER TABLE market_quotes MATERIALIZE TTL;
```

### 日志检查

```bash
# 查看ClickHouse日志
tail -f /var/log/clickhouse-server/clickhouse-server.log

# 查看应用日志
tail -f logs/consumer.log
```

## 📈 性能优化

### 1. 查询优化

- **使用分区剪枝**：WHERE条件包含时间字段
- **合理使用索引**：利用主键和二级索引
- **避免SELECT \***：只查询需要的字段
- **使用物化视图**：预聚合常用查询

### 2. 写入优化

- **批量写入**：使用batch_insert_market_quotes
- **异步写入**：启用async_insert
- **压缩优化**：选择合适的压缩算法
- **分区策略**：合理设置分区大小

### 3. 存储优化

- **TTL设置**：自动清理过期数据
- **压缩配置**：平衡压缩率和性能
- **副本配置**：生产环境建议使用副本

## 🧪 单元测试

```bash
# 运行ClickHouse集成测试
python -m pytest tests/test_clickhouse_integration.py -v

# 运行特定测试
python -m pytest tests/test_clickhouse_integration.py::TestClickHouseManager::test_insert_market_quote -v

# 运行所有数据库相关测试
python -m pytest tests/ -k "clickhouse or database" -v
```

## 📋 迁移检查清单

- [ ] 安装ClickHouse服务器
- [ ] 安装Python依赖包
- [ ] 配置数据库连接参数
- [ ] 运行初始化脚本
- [ ] 执行功能测试
- [ ] 配置数据库类型或双写模式
- [ ] 启动消费者服务
- [ ] 验证数据写入和查询
- [ ] 设置监控和告警
- [ ] 配置数据备份策略

## 🆘 技术支持

如遇到问题，请检查：

1. **服务状态**：`python scripts/test_clickhouse.py --test connection`
2. **配置文件**：确认database.yaml配置正确
3. **网络连接**：检查防火墙和端口设置
4. **权限设置**：确认数据库用户权限
5. **日志文件**：查看详细错误信息

更多信息请参考：
- [ClickHouse官方文档](https://clickhouse.com/docs/)
- [clickhouse-driver文档](https://clickhouse-driver.readthedocs.io/)
- 项目日志文件：`logs/consumer.log`
