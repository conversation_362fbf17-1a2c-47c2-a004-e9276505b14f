# QMT数据收集与处理系统

本项目是一个完整的股票市场数据收集、处理和存储系统，采用微服务架构，支持实时数据流处理。

## 系统架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    QMT      │───▶│  Producer   │───▶│  RabbitMQ   │───▶│  Consumer   │
│  数据源     │    │   服务      │    │   消息队列  │    │   服务      │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
                                                                │
                                                                ▼
                   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
                   │ Prometheus  │◀───│   MySQL     │    │  Grafana    │
                   │   监控      │    │   数据库    │───▶│  可视化     │
                   └─────────────┘    └─────────────┘    └─────────────┘
```

## 功能特性

### Producer服务 (数据生产者)
- ✅ 实时订阅QMT股票行情数据
- ✅ 将数据发送到RabbitMQ队列
- ✅ Prometheus监控指标暴露
- ✅ HTTP健康检查接口
- ✅ 系统资源监控 (CPU、内存、磁盘、网络)
- ✅ 错误处理和重连机制
- ✅ 完整的单元测试覆盖

### Consumer服务 (数据消费者)
- ✅ 从RabbitMQ消费市场数据消息
- ✅ 存储数据到MySQL数据库
- ✅ Prometheus监控指标暴露
- ✅ HTTP健康检查接口
- ✅ 连接池管理和批量处理
- ✅ 优雅关闭和资源清理

### 监控和运维
- ✅ Prometheus指标收集
- ✅ Grafana可视化仪表板
- ✅ 健康检查和存活性检测
- ✅ 详细的日志记录
- ✅ Docker容器化部署

## 项目结构

```
qmt_data_collect/
├── producer/                 # 数据生产者服务
│   ├── rabbitmq_client.py   # RabbitMQ客户端
│   ├── test.py              # 主程序
│   └── test_rabbitmq_client.py # 单元测试
├── consumer/                 # 数据消费者服务
│   ├── main.py              # 主服务程序
│   ├── database_manager.py  # 数据库管理器
│   ├── rabbitmq_consumer.py # RabbitMQ消费者
│   ├── metrics.py           # Prometheus指标
│   ├── health_check.py      # 健康检查
│   ├── test_consumer.py     # 单元测试
│   └── README.md            # 消费者文档
├── config/                   # 配置文件目录
│   ├── database.yaml        # 数据库配置
│   ├── database_schema.sql  # 数据库表结构
│   └── rabbitmq_config.yaml # RabbitMQ配置
├── monitoring/               # 监控配置
│   └── prometheus.yml       # Prometheus配置
├── logs/                     # 日志目录
├── config.yaml              # RabbitMQ配置
├── docker-compose.yml       # Docker编排文件
├── Dockerfile               # Docker镜像构建
├── requirements.txt         # Python依赖
└── README.md               # 项目文档
```

## 快速开始

### 1. 使用Docker Compose (推荐)

```bash
# 克隆项目
git clone <repository-url>
cd qmt_data_collect

# 启动完整系统
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f consumer
```

启动后可访问：
- **生产者健康检查**: http://localhost:8081/health
- **消费者健康检查**: http://localhost:8080/health
- **Prometheus监控**: http://localhost:9090
- **Grafana仪表板**: http://localhost:3000 (admin/admin)
- **RabbitMQ管理界面**: http://localhost:15672 (guest/guest)

### 2. 本地开发环境

```bash
# 安装依赖
pip install -r requirements.txt

# 启动数据库和RabbitMQ (使用Docker)
docker-compose up -d mysql rabbitmq

# 等待服务启动完成
sleep 30

# 启动消费者服务
cd consumer
python main.py

# 在另一个终端启动生产者
python test.py
```

## 配置说明

### 1. RabbitMQ配置 (config/rabbitmq_config.yaml)

```yaml
rabbitmq:
  host: "localhost"
  port: 5672
  username: "guest"
  password: "guest"
  virtual_host: "/"

  queue:
    name: "market_data_queue"
    durable: true

  exchange:
    name: "market_data_exchange"
    type: "direct"

  routing_key: "market.data"

  connection:
    heartbeat: 600
    blocked_connection_timeout: 300
    socket_timeout: 10

app:
  data_format: "json"
  batch_size: 50
  flush_interval: 2
```

### 2. 数据库配置 (config/database.yaml)

```yaml
database:
  primary:
    host: "localhost"
    port: 3306
    username: "root"
    password: "password"
    database: "market_data"

  pool:
    min_connections: 5
    max_connections: 20
```

## 数据库表结构

系统会自动创建以下表：

- **stock_info**: 股票基本信息
- **market_quotes**: 实时行情主表
- **market_depth**: 买卖盘口数据
- **processing_log**: 数据处理日志
- **system_metrics**: 系统监控指标

详细表结构请参考 `config/database_schema.sql`

## 代码示例

### 基本使用

```python
from rabbitmq_client import RabbitMQClient

# 创建客户端
client = RabbitMQClient("config.yaml")

# 连接到RabbitMQ
if client.connect():
    # 发送消息
    data = {"symbol": "000001.SZ", "price": 11.75}
    client.send_message(data)
    
    # 断开连接
    client.disconnect()
```

### 使用上下文管理器

```python
from rabbitmq_client import RabbitMQClient

with RabbitMQClient("config.yaml") as client:
    data = {"symbol": "000001.SZ", "price": 11.75}
    client.send_message(data)
```

### 批量发送

```python
from rabbitmq_client import RabbitMQClient

client = RabbitMQClient("config.yaml")
client.connect()

data_list = [
    {"symbol": "000001.SZ", "price": 11.75},
    {"symbol": "000002.SZ", "price": 25.30}
]

success_count = client.send_batch_messages(data_list)
print(f"成功发送 {success_count} 条消息")
```

## 消息格式

发送到RabbitMQ的消息格式：

```json
{
    "timestamp": "2025-01-01T10:30:00.123456",
    "data": {
        "symbol": "000001.SZ",
        "time": 1640995200000,
        "open": 11.73,
        "high": 11.77,
        "low": 11.70,
        "close": 11.75,
        "volume": 1000000,
        "amount": 11750000.0,
        "settelementPrice": 0.0,
        "openInterest": 14,
        "preClose": 11.70,
        "suspendFlag": 0
    }
}
```

## 错误处理

程序包含完善的错误处理机制：

- **连接失败**: 自动重试连接
- **发送失败**: 记录错误日志
- **配置错误**: 详细的错误提示
- **网络中断**: 自动重连机制

## 日志配置

日志文件位置：`logs/rabbitmq_client.log`

可以通过配置文件调整日志级别：

```yaml
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/rabbitmq_client.log"
```

## 性能优化

- 使用连接池减少连接开销
- 批量发送消息提高吞吐量
- 消息持久化保证数据安全
- 异步处理避免阻塞

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查RabbitMQ服务是否运行
   - 验证用户名和密码
   - 确认端口是否正确

2. **队列不存在**
   - 程序会自动创建队列
   - 检查权限设置

3. **消息发送失败**
   - 查看日志文件
   - 检查网络连接
   - 验证配置文件

## 开发和测试

### 运行测试

```bash
# 安装依赖并运行测试
python run_tests.py --install

# 仅运行测试
python run_tests.py

# 运行特定测试
pytest test_rabbitmq_client.py::TestRabbitMQClient::test_connect_success -v
```

### 代码覆盖率

```bash
pytest test_rabbitmq_client.py --cov=rabbitmq_client --cov-report=html
```

## 许可证

MIT License
