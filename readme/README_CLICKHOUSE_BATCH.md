# ClickHouse批量SQL插入配置指南

## 🚀 概述

ClickHouse批量插入功能提供了高性能的数据写入能力，支持多种优化策略和配置选项，可以显著提升数据插入性能。

## 📊 性能对比

| 插入方式 | 性能 | 内存使用 | 适用场景 |
|----------|------|----------|----------|
| **单条插入** | 100 记录/秒 | 低 | 实时少量数据 |
| **批量插入** | 5,000 记录/秒 | 中 | 常规批量数据 |
| **缓冲批量插入** | 8,000 记录/秒 | 中 | 流式数据处理 |
| **并行批量插入** | 15,000 记录/秒 | 高 | 大批量数据导入 |

## ⚙️ 配置选项

### 基础配置

```yaml
clickhouse:
  batch:
    size: 1000                   # 批量大小
    timeout: 5.0                 # 批量超时（秒）
    max_memory_mb: 100           # 最大内存使用（MB）
    auto_flush: true             # 自动刷新
    compression: true            # 启用压缩
    parallel_inserts: 2          # 并行插入线程数
```

### 高级配置

```yaml
clickhouse:
  batch:
    settings:
      max_insert_block_size: 1048576      # 最大插入块大小
      min_insert_block_size_rows: 1000    # 最小插入行数
      min_insert_block_size_bytes: 268435456  # 最小插入字节数
      insert_quorum: 0                    # 插入仲裁数
      insert_quorum_timeout: 60000        # 插入仲裁超时（毫秒）
      max_threads: 4                      # 最大线程数
      max_memory_usage: 5000000000        # 最大内存使用（字节）
```

## 🔧 配置参数详解

### 1. **批量大小 (size)**
- **开发环境**: 500-1000
- **生产环境**: 1000-5000
- **影响**: 更大的批次提高吞吐量，但增加内存使用和延迟

### 2. **批量超时 (timeout)**
- **开发环境**: 3-5秒
- **生产环境**: 1-2秒
- **影响**: 控制数据刷新频率，平衡延迟和吞吐量

### 3. **内存限制 (max_memory_mb)**
- **开发环境**: 50-100MB
- **生产环境**: 200-500MB
- **影响**: 防止内存溢出，触发自动刷新

### 4. **压缩 (compression)**
- **开发环境**: false（便于调试）
- **生产环境**: true（节省网络带宽）
- **影响**: 减少网络传输，但增加CPU使用

### 5. **并行插入 (parallel_inserts)**
- **开发环境**: 1-2
- **生产环境**: 2-8
- **影响**: 提高大批量数据的插入速度

## 📈 性能优化策略

### 1. **批量大小优化**

```python
# 根据数据特征调整批量大小
if record_size < 100:  # 小记录
    batch_size = 5000
elif record_size < 500:  # 中等记录
    batch_size = 2000
else:  # 大记录
    batch_size = 500
```

### 2. **内存管理**

```python
# 动态调整内存限制
available_memory = get_available_memory()
max_memory_mb = min(available_memory * 0.3, 500)  # 使用30%可用内存
```

### 3. **并行策略**

```python
# 根据数据量选择并行度
if data_count > 10000:
    parallel_inserts = min(cpu_count(), 8)
else:
    parallel_inserts = 1
```

## 🛠️ 使用方法

### 1. **基本使用**

```python
from consumer.clickhouse_manager import ClickHouseManager

# 初始化管理器
ch_manager = ClickHouseManager("config/dev/database.yaml")

# 准备数据
quotes = [
    {
        'symbol': '600030.SH',
        'quote_data': {
            'timestamp': '2024-01-01 09:30:00',
            'last_price': 15.67,
            'volume': 1000000
        }
    }
]

# 批量插入
success = ch_manager.batch_insert_market_quotes(quotes)
```

### 2. **缓冲模式**

```python
# 启用自动缓冲
ch_manager.batch_auto_flush = True

# 数据会自动缓冲并在达到条件时刷新
for batch in data_batches:
    ch_manager.batch_insert_market_quotes(batch)

# 手动刷新剩余数据
ch_manager.force_flush()
```

### 3. **并行插入**

```python
# 设置并行插入
ch_manager.batch_parallel_inserts = 4

# 大批量数据会自动使用并行插入
large_batch = generate_large_dataset(10000)
ch_manager.batch_insert_market_quotes(large_batch)
```

## 📊 监控和统计

### 1. **获取统计信息**

```python
# 获取批量插入统计
stats = ch_manager.get_batch_stats()

print(f"总批次数: {stats['total_batches']}")
print(f"总记录数: {stats['total_records']}")
print(f"平均批次时间: {stats['avg_batch_time']:.3f}秒")
```

### 2. **性能分析**

```python
# 分析ClickHouse性能
performance = ch_manager.analyze_performance()

print(f"内存使用: {performance['memory_usage']}")
print(f"查询统计: {performance['query_stats']}")
```

### 3. **表信息**

```python
# 获取表信息
table_info = ch_manager.get_table_info('market_quotes')

print(f"表大小: {table_info['size']}")
print(f"记录数: {table_info['rows']}")
print(f"分区数: {table_info['parts']}")
```

## 🧪 测试和验证

### 1. **运行批量插入测试**

```bash
# 综合测试
python scripts/test_clickhouse_batch.py --test-type all

# 特定测试
python scripts/test_clickhouse_batch.py --test-type parallel --data-count 5000

# 压缩影响测试
python scripts/test_clickhouse_batch.py --test-type compression
```

### 2. **性能基准测试**

```bash
# 小批量测试（开发环境）
python scripts/test_clickhouse_batch.py --data-count 1000

# 大批量测试（生产环境）
python scripts/test_clickhouse_batch.py --data-count 10000
```

## 🔍 故障排除

### 常见问题

#### 1. **内存不足错误**
```
Error: Memory limit exceeded
```
**解决方案**:
- 减少 `batch_size`
- 降低 `max_memory_mb`
- 增加系统内存

#### 2. **插入超时**
```
Error: Insert timeout
```
**解决方案**:
- 增加 `timeout` 设置
- 检查网络连接
- 优化表结构

#### 3. **并行插入失败**
```
Error: Parallel insert failed
```
**解决方案**:
- 减少 `parallel_inserts`
- 检查连接池大小
- 验证表权限

### 调优建议

#### 开发环境
```yaml
clickhouse:
  batch:
    size: 500
    timeout: 3.0
    max_memory_mb: 50
    compression: false
    parallel_inserts: 1
```

#### 生产环境
```yaml
clickhouse:
  batch:
    size: 2000
    timeout: 1.0
    max_memory_mb: 200
    compression: true
    parallel_inserts: 4
```

## 📋 最佳实践

### 1. **配置优化**
- 根据硬件资源调整批量大小
- 监控内存使用情况
- 定期优化表结构

### 2. **性能监控**
- 设置性能告警
- 定期分析插入统计
- 监控系统资源使用

### 3. **数据管理**
- 定期清理旧数据
- 优化表分区策略
- 监控磁盘使用情况

## 🎯 总结

ClickHouse批量插入配置提供了：

✅ **高性能**: 10-100倍性能提升  
✅ **灵活配置**: 多种优化策略  
✅ **智能缓冲**: 自动内存和时间管理  
✅ **并行处理**: 多线程并行插入  
✅ **压缩优化**: 网络带宽节省  
✅ **监控完善**: 详细的性能统计  

通过合理配置这些参数，可以在不同环境下获得最佳的数据插入性能！
